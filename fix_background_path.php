<?php
/**
 * إصلاح مسار صورة الخلفية في قاعدة البيانات
 * Fix background image path in database
 */

require_once 'db_connection.php';

try {
    // التحقق من وجود جدول الإعدادات
    $tableExists = $conn->query("SHOW TABLES LIKE 'sticker_settings'");
    
    if ($tableExists->num_rows > 0) {
        // البحث عن إعداد صورة الخلفية
        $stmt = $conn->prepare("SELECT * FROM sticker_settings WHERE setting_key = 'background_image'");
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $currentPath = $row['setting_value'];
            echo "<h3>المسار الحالي: " . htmlspecialchars($currentPath) . "</h3>";
            
            // إصلاح المسار إذا كان يبدأ بـ /
            if (strpos($currentPath, '/') === 0) {
                $newPath = ltrim($currentPath, '/');
                
                $updateStmt = $conn->prepare("UPDATE sticker_settings SET setting_value = ? WHERE setting_key = 'background_image'");
                $updateStmt->bind_param("s", $newPath);
                
                if ($updateStmt->execute()) {
                    echo "<div style='color: green;'>✅ تم تحديث المسار إلى: " . htmlspecialchars($newPath) . "</div>";
                } else {
                    echo "<div style='color: red;'>❌ فشل في تحديث المسار</div>";
                }
                $updateStmt->close();
            } else {
                echo "<div style='color: blue;'>ℹ️ المسار صحيح بالفعل</div>";
            }
        } else {
            // إدراج الإعداد الافتراضي
            $defaultPath = 'Eyad.png';
            $description = 'صورة خلفية الاستيكر';
            
            $insertStmt = $conn->prepare("INSERT INTO sticker_settings (setting_key, setting_value, description) VALUES ('background_image', ?, ?)");
            $insertStmt->bind_param("ss", $defaultPath, $description);
            
            if ($insertStmt->execute()) {
                echo "<div style='color: green;'>✅ تم إنشاء إعداد صورة الخلفية: " . htmlspecialchars($defaultPath) . "</div>";
            } else {
                echo "<div style='color: red;'>❌ فشل في إنشاء الإعداد</div>";
            }
            $insertStmt->close();
        }
        $stmt->close();
    } else {
        echo "<div style='color: orange;'>⚠️ جدول الإعدادات غير موجود</div>";
    }
    
    // اختبار وجود الملف
    $imagePaths = ['Eyad.png', './Eyad.png', __DIR__ . '/Eyad.png'];
    
    echo "<h3>اختبار وجود الملف:</h3>";
    foreach ($imagePaths as $path) {
        if (file_exists($path)) {
            echo "<div style='color: green;'>✅ الملف موجود: " . htmlspecialchars($path) . "</div>";
        } else {
            echo "<div style='color: red;'>❌ الملف غير موجود: " . htmlspecialchars($path) . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>خطأ: " . htmlspecialchars($e->getMessage()) . "</div>";
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مسار صورة الخلفية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        
        .sticker-preview {
            width: 60mm;
            height: 35mm;
            border: 2px dashed #ccc;
            margin: 10px 0;
            background-image: url('Eyad.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
            color: black;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إصلاح مسار صورة الخلفية</h1>
        
        <div class="test-section">
            <h3>معاينة الاستيكر</h3>
            <div class="sticker-preview">
                معاينة الخلفية
            </div>
        </div>
        
        <div class="test-section">
            <h3>اختبار الإعدادات</h3>
            <button onclick="testSettings()">اختبار تحميل الإعدادات</button>
            <button onclick="updateSettings()">تحديث الإعدادات</button>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>روابط مفيدة</h3>
            <a href="test_sticker_background.html" target="_blank">صفحة اختبار الخلفية</a><br>
            <a href="sticker_settings.php" target="_blank">إعدادات الاستيكر</a><br>
            <a href="items.php" target="_blank">صفحة الأصناف</a>
        </div>
    </div>
    
    <script>
        function testSettings() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div style="color: blue;">جاري الاختبار...</div>';
            
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_settings'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div style="color: green;">✅ تم تحميل الإعدادات بنجاح</div>
                        <div><strong>صورة الخلفية:</strong> ${data.settings.background_image}</div>
                        <div><strong>العرض:</strong> ${data.settings.width}</div>
                        <div><strong>الارتفاع:</strong> ${data.settings.height}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div style="color: red;">❌ فشل في تحميل الإعدادات</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div style="color: red;">❌ خطأ: ${error.message}</div>`;
            });
        }
        
        function updateSettings() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = '<div style="color: blue;">جاري التحديث...</div>';
            
            const settings = {
                background_image: 'Eyad.png',
                width: '60mm',
                height: '35mm'
            };
            
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_settings&settings=${encodeURIComponent(JSON.stringify(settings))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div style="color: green;">✅ تم تحديث الإعدادات بنجاح</div>';
                } else {
                    resultDiv.innerHTML = `<div style="color: red;">❌ فشل في التحديث: ${JSON.stringify(data.errors)}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div style="color: red;">❌ خطأ: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
