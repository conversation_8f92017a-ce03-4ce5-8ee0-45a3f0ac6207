/**
 * نظام الاستيكر الموحد
 * Unified Sticker System
 * 
 * يحتوي على جميع وظائف الاستيكر المحسنة
 */

// متغيرات عامة
let printWindowRef = null;
const STICKER_CONFIG = {
    width: '60mm',
    height: '35mm',
    backgroundImage: '/Eyad.png',
    brandName: 'AN NAJJAR',
    currency: 'JOD'
};

/**
 * دالة مساعدة لضبط حجم الخط حسب طول النص
 */
function adjustFontSize(element, baseSize = 16, maxChars = 20, reductionFactor = 0.3, minSize = 8) {
    if (!element) return;
    
    const textLength = element.textContent.length;
    const extraChars = textLength - maxChars;
    let newSize = baseSize;
    
    if (extraChars > 0) {
        newSize = baseSize - (extraChars * reductionFactor);
        newSize = Math.max(newSize, minSize);
    }
    
    element.style.fontSize = newSize + "px";
}

/**
 * دالة لتنسيق اسم المنتج (إضافة فواصل الأسطر)
 */
function formatProductName(itemName) {
    if (!itemName) return '';
    
    // إضافة فاصل سطر بين (FREM) و (OLED)
    itemName = itemName.replace("(FREM) (OLED)", "(FREM)<br>(OLED)");
    
    // إضافة فاصل سطر للأسماء الطويلة
    if (itemName.replace(/<br>/g, "").length > 20) {
        const mid = Math.floor(itemName.replace(/<br>/g, "").length / 2);
        let idx = itemName.indexOf(" ", mid);
        if (idx === -1) idx = mid;
        itemName = itemName.substring(0, idx) + "<br>" + itemName.substring(idx + 1);
    }
    
    return itemName;
}

/**
 * دالة لحساب عرض الباركود
 */
function calculateBarcodeWidth(barcodeValue) {
    let calcWidth = 2;
    if (barcodeValue.length > 10) {
        calcWidth = Math.max(2 * (10 / barcodeValue.length), 0.5);
    }
    return calcWidth * 0.8;
}

/**
 * دالة لإنشاء محتوى الاستيكر
 */
function createStickerContent(itemName, barcodeValue, priceValue, qrValue = "") {
    const formattedName = formatProductName(itemName);
    const priceHTML = priceValue.trim() ? `${priceValue} ${STICKER_CONFIG.currency}` : "";
    const qrCode = qrValue || barcodeValue;
    
    return `
        <div id="stickerContent" class="sticker-content"
             style="
               width: 100%;
               height: 100%;
               position: relative;
               background: url('${STICKER_CONFIG.backgroundImage}') no-repeat center center;
               background-size: cover;
               box-sizing: border-box;
             ">

          <!-- رمز QR في أعلى اليمين -->
          <div id="topRightQRCode" style="
               position: absolute;
               top: 2mm;
               right: 2mm;
          "></div>

          <!-- اسم العلامة التجارية -->
          <div style="
               position: absolute;
               top: 2mm;
               left: 50%;
               transform: translateX(-50%);
               font-weight: bold;
               font-size: 14px;
               color: black;
          ">
            ${STICKER_CONFIG.brandName}
          </div>

          <!-- اسم الصنف -->
          <div id="stickerProductName"
               style="
                 position: absolute;
                 top: 10mm;
                 left: 50%;
                 transform: translateX(-50%);
                 font-size: 12px;
                 font-weight: bold;
                 color: black;
                 text-align: center;
                 width: 70%;
                 white-space: normal;
                 word-wrap: break-word;
                 display: -webkit-box;
                 -webkit-box-orient: vertical;
                 -webkit-line-clamp: 2;
                 overflow: hidden;
               ">
            ${formattedName}
          </div>

          <!-- السعر -->
          <div id="productDetails"
               style="
                 position: absolute;
                 top: 15mm;
                 left: 50%;
                 transform: translateX(-50%);
                 font-size: 12px;
                 font-weight: bold;
                 color: black;
                 text-align: center;
               ">
            ${priceHTML}
          </div>

          <!-- الباركود -->
          <div style="
               position: absolute;
               bottom: 5mm; 
               left: 50%;
               transform: translateX(-50%);
               text-align: center;
               width: 100%;
               display: flex;
               justify-content: center;
               align-items: center;
          ">
            <svg id="stickerSVG"></svg>
            <div id="barcodeNumber"
                 style="
                   font-size: 14px;
                   font-family: 'Arial, sans-serif';
                   font-weight: bold;
                   margin-top: 2px;
                   color: #000;
                 ">
            </div>
          </div>
        </div>
    `;
}

/**
 * دالة لفتح نافذة معاينة الاستيكر
 */
function openStickerModal(itemName, barcodeValue, priceValue, qrValue = "") {
    const label = document.getElementById("stickerItemLabel");
    if (!label) {
        console.error("لم يتم العثور على العنصر stickerItemLabel.");
        return;
    }

    // إنشاء محتوى الاستيكر
    label.className = "sticker-label";
    label.innerHTML = createStickerContent(itemName, barcodeValue, priceValue, qrValue);

    // ضبط حجم خط اسم الصنف
    const productNameElem = document.getElementById("stickerProductName");
    if (productNameElem) {
        if (itemName.indexOf("<br>") !== -1) {
            adjustFontSize(productNameElem, 10, 20, 0.3, 6);
        } else {
            adjustFontSize(productNameElem, 16, 20, 0.3, 8);
        }
    }

    // حفظ قيمة الباركود
    const barcodeTextElem = document.getElementById("actualBarcode");
    if (barcodeTextElem) {
        barcodeTextElem.textContent = barcodeValue;
    }

    // توليد الباركود
    generateBarcode(barcodeValue);
    
    // توليد رمز QR
    generateQRCode(qrValue || barcodeValue);

    // ضبط أبعاد الملصق
    const sticker = document.getElementById("stickerPreview");
    if (sticker) {
        sticker.style.width = STICKER_CONFIG.width;
        sticker.style.height = STICKER_CONFIG.height;
        sticker.style.position = "relative";
    }
}

/**
 * دالة لتوليد الباركود
 */
function generateBarcode(barcodeValue) {
    const svgElem = document.getElementById("stickerSVG");
    if (!svgElem) return;

    svgElem.innerHTML = "";
    const barcodeWidth = calculateBarcodeWidth(barcodeValue);
    
    try {
        JsBarcode("#stickerSVG", barcodeValue, {
            format: "CODE128",
            lineColor: "#000",
            width: barcodeWidth,
            height: 50,
            displayValue: true,
            fontOptions: "bold",
            fontSize: 14,
            textMargin: 5
        });
    } catch (error) {
        console.error("خطأ في توليد الباركود:", error);
    }

    // عرض رقم الباركود نصياً
    const barcodeNumberElem = document.getElementById("barcodeNumber");
    if (barcodeNumberElem) {
        barcodeNumberElem.textContent = barcodeValue;
    }
}

/**
 * دالة لتوليد رمز QR
 */
function generateQRCode(qrValue) {
    const topRightQR = document.getElementById("topRightQRCode");
    if (!topRightQR) return;

    topRightQR.innerHTML = "";
    
    try {
        new QRCode(topRightQR, {
            text: qrValue,
            width: 50,
            height: 50
        });
    } catch (error) {
        console.error("خطأ في توليد رمز QR:", error);
    }
}

/**
 * دالة طباعة الاستيكر
 */
function printSticker() {
    // منع فتح نافذة طباعة ثانية إذا كانت الأولى مفتوحة
    if (printWindowRef && !printWindowRef.closed) {
        printWindowRef.focus();
        return;
    }

    const sticker = document.getElementById("stickerPreview");
    if (!sticker) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لم يتم العثور على معاينة الاستيكر'
        });
        return;
    }

    const barcodeValue = document.getElementById("actualBarcode")?.textContent || "";
    if (!barcodeValue) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لم يتم العثور على الباركود'
        });
        return;
    }

    const productName = document.getElementById("stickerProductName")?.innerHTML || "";
    const productPrice = document.getElementById("productDetails")?.textContent || "";

    // حساب عرض الباركود
    const barcodeWidth = calculateBarcodeWidth(barcodeValue);

    // فتح نافذة طباعة وتخزين مرجعها
    printWindowRef = window.open("", "", "height=400,width=400");
    if (!printWindowRef) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لا يمكن فتح نافذة الطباعة. يرجى إلغاء حظر النوافذ المنبثقة.'
        });
        return;
    }

    // إنشاء محتوى الطباعة
    const printContent = createPrintContent(productName, productPrice, barcodeValue, barcodeWidth);

    printWindowRef.document.write(printContent);
    printWindowRef.document.close();
}

/**
 * دالة إنشاء محتوى الطباعة
 */
function createPrintContent(productName, productPrice, barcodeValue, barcodeWidth) {
    return `
        <html>
        <head>
            <title>طباعة الاستيكر</title>
            <style>
                @page {
                    size: ${STICKER_CONFIG.width} ${STICKER_CONFIG.height};
                    margin: 0;
                }
                body {
                    margin: 0;
                    padding: 0;
                    font-family: ${STICKER_CONFIG.fontFamily || 'Arial, sans-serif'};
                }
                .sticker-print {
                    position: relative;
                    width: ${STICKER_CONFIG.width};
                    height: ${STICKER_CONFIG.height};
                    background: url('${STICKER_CONFIG.backgroundImage}') no-repeat center center;
                    background-size: cover;
                    overflow: hidden;
                }
                .qr-top-right {
                    position: absolute;
                    top: 2mm;
                    right: 2mm;
                }
                .product-info {
                    position: absolute;
                    top: 5mm;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 90%;
                    text-align: center;
                }
                .product-name {
                    font-size: 11px;
                    font-weight: bold;
                    margin-bottom: 2mm;
                    line-height: 1.2;
                }
                .product-price {
                    font-size: 11px;
                    font-weight: bold;
                    color: #000;
                }
                .barcode-container {
                    position: absolute;
                    bottom: 2mm;
                    left: 50%;
                    transform: translateX(-50%);
                    text-align: center;
                    width: 90%;
                }
                svg {
                    display: inline-block;
                    margin: 0 auto;
                }
            </style>
            <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
        </head>
        <body>
            <div class="sticker-print">
                <div class="qr-top-right" id="printQRCode"></div>
                <div class="product-info">
                    <div class="product-name">${productName}</div>
                    <div class="product-price">${productPrice}</div>
                </div>
                <div class="barcode-container">
                    <svg id="printBarcode"></svg>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // توليد الباركود
                    JsBarcode('#printBarcode', '${barcodeValue}', {
                        format: '${STICKER_CONFIG.barcodeFormat || 'CODE128'}',
                        lineColor: '#000',
                        width: ${barcodeWidth},
                        height: 18,
                        displayValue: true,
                        fontOptions: 'bold',
                        fontSize: 14,
                        margin: 0
                    });

                    // توليد رمز QR
                    new QRCode(document.getElementById('printQRCode'), {
                        text: '${barcodeValue}',
                        width: 50,
                        height: 50
                    });

                    // طباعة تلقائية
                    window.print();

                    // إغلاق النافذة بعد الطباعة
                    setTimeout(() => window.close(), 1000);
                });
            </script>
        </body>
        </html>
    `;
}

/**
 * دالة اختيار السعر وطباعة الاستيكر مباشرة
 */
function choosePriceAndPrint(itemName, barcodeValue, price1, price2, price3, price4) {
    Swal.fire({
        title: 'اختر السعر',
        input: 'select',
        inputOptions: {
            '0': 'بدون سعر',
            '1': 'السعر الأول: ' + price1,
            '2': 'السعر الثاني: ' + price2,
            '3': 'السعر الثالث: ' + price3,
            '4': 'السعر الرابع: ' + price4
        },
        inputPlaceholder: 'اختر السعر',
        showCancelButton: true,
        confirmButtonText: 'طباعة',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed && result.value !== undefined) {
            let selectedPrice = "";
            switch (result.value) {
                case '0': selectedPrice = ""; break;
                case '1': selectedPrice = price1; break;
                case '2': selectedPrice = price2; break;
                case '3': selectedPrice = price3; break;
                case '4': selectedPrice = price4; break;
                default: selectedPrice = price1;
            }

            // إنشاء الاستيكر وطباعته مباشرة
            openStickerModal(itemName, barcodeValue, selectedPrice);
            setTimeout(() => printSticker(), 100); // تأخير بسيط للتأكد من تحميل المحتوى
        }
    });
}

// تصدير الدوال للاستخدام العام
window.StickerSystem = {
    openStickerModal,
    printSticker,
    choosePriceAndPrint,
    adjustFontSize,
    formatProductName,
    generateBarcode,
    generateQRCode,
    createPrintContent,
    STICKER_CONFIG
};

// تحميل المكتبات المطلوبة
document.addEventListener("DOMContentLoaded", function() {
    // التأكد من تحميل مكتبة QRCode
    if (typeof QRCode === "undefined") {
        const script = document.createElement('script');
        script.src = "https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js";
        document.head.appendChild(script);
    }
    
    // جعل الدوال متاحة عالمياً للتوافق مع الكود الحالي
    window.openStickerModal = openStickerModal;
    window.printSticker = printSticker;
    window.choosePriceAndPrint = choosePriceAndPrint;
});
