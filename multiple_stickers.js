function printMultipleStickers(stickersArray) {
    // stickersArray: [{itemName, barcode, price}, ...]

    // التحقق من صحة البيانات
    if (!stickersArray || !Array.isArray(stickersArray) || stickersArray.length === 0) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لا توجد ملصقات للطباعة'
        });
        return;
    }

    var printWindow = window.open("", "", "height=800,width=600");
    if (!printWindow) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لا يمكن فتح نافذة الطباعة. يرجى إلغاء حظر النوافذ المنبثقة.'
        });
        return;
    }

    let stickersHtml = "";

    stickersArray.forEach(sticker => {
        let itemName = sticker.itemName || "";
        let barcode = sticker.barcode || "";
        let priceValue = sticker.price || "";

        // Automatically insert a line break between (FREM) and (OLED)
        itemName = itemName.replace("(FREM) (OLED)", "(FREM)<br>(OLED)");
        // If the product name is long, insert a <br> to force text onto the next line
        if (itemName.replace(/<br>/g, "").length >= 20) { // updated condition for long names
            let mid = Math.floor(itemName.replace(/<br>/g, "").length / 2);
            let idx = itemName.indexOf(" ", mid);
            if (idx === -1) idx = mid;
            itemName = itemName.substring(0, idx) + "<br>" + itemName.substring(idx + 1);
        }

        // Determine extra margin for price when product name is one line
        let priceMargin = "";
        if (itemName.indexOf("<br>") === -1) {
            priceMargin = "margin-top: 1em;";
        }

        // تجهيز السعر
        let priceHTML = priceValue.trim() ? `${priceValue} JOD` : "";

        // نص الاستيكر (نفس الشكل من دالة الاستيكر الواحد)
        stickersHtml += `
          <div class="sticker-print"
               style="
                 position: relative;
                 width: 60mm;   /* updated width */
                 height: 35mm;  /* updated height */
                 background: url('/Eyad.png') no-repeat center center;
                 background-size: cover;
                 box-sizing: border-box;
                 overflow: hidden;
                 font-family: sans-serif;
                 page-break-after: always; /* ضع فاصل صفحة بعد كل استيكر */
               ">

            <!-- QR أعلى اليمين -->
            <div style="position: absolute; top: 2mm; right: 2mm;" id="qr_${barcode}"></div>

            <!-- Combined Product Name & Price Container -->
            <div id="productInfo_${barcode}" style="
                 position: absolute;
                 top: 5mm;
                 left: 50%;
                 transform: translateX(-50%);
                 text-align: center;
                 width: 70%;
            ">
              <div id="productName_${barcode}" style="
                 font-size: 12px;
                 font-weight: bold;
                 color: black;
                 white-space: normal;
                 word-wrap: break-word;
                 display: -webkit-box;
                 -webkit-box-orient: vertical;
                 -webkit-line-clamp: 3;  /* allow three lines */
                 overflow: hidden;
              ">
                ${itemName}
              </div>
              <div id="productPrice_${barcode}" style="
                 font-size: 12px;
                 font-weight: bold;
                 color: black;
                 ${priceMargin}
              ">
                ${priceHTML}
              </div>
            </div>

            <!-- الباركود -->
            <div style="
                 position: absolute;
                 bottom: 0.5mm;
                 left: 50%;
                 transform: translateX(-50%);
                 text-align: center;
                 width: 100%;
                 display: flex;
                 justify-content: center;
                 align-items: center;
            ">
              <svg id="barcode_${barcode}"></svg>
            </div>
          </div>
        `;
    });

    // كتابة الـ HTML + CSS في نافذة الطباعة
    printWindow.document.write(`
      <html>
      <head>
        <title>Multiple Stickers</title>
        <style>
          @page {
            /* اضبط حجم الصفحة على حجم الاستيكر */
            size: 60mm 35mm;  /* updated page size */
            margin: 0; 
          }
          body {
            margin: 0;
            padding: 0;
          }
          .sticker-print {
            page-break-inside: avoid; 
          }
          svg {
            display: inline-block;
            margin: 0 auto;
          }
        </style>
        <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
      </head>
      <body>
        ${stickersHtml}
        <script>
          document.addEventListener('DOMContentLoaded', function() {
              // توليد الباركود
              var barcodeSvgs = document.querySelectorAll('[id^="barcode_"]');
              barcodeSvgs.forEach(function(svg) {
                  var code = svg.id.replace('barcode_', '');
                  var calcWidth = 2;
                  if (code.length > 10) {
                      calcWidth = Math.max(2 * (10 / code.length), 0.5);
                  }
                  var newWidth = calcWidth * 0.8;
                  JsBarcode(svg, code, {
                      format: 'CODE128',
                      lineColor: '#000',
                      width: newWidth,
                      height: 30,
                      displayValue: true,
                      fontOptions: 'bold',
                      fontSize: 14,
                      margin: 0
                  });
              });

              // توليد الـ QR
              var qrDivs = document.querySelectorAll('[id^="qr_"]');
              qrDivs.forEach(function(div) {
                  var code = div.id.replace('qr_', '');
                  new QRCode(div, {
                      text: code,
                      width: 50,
                      height: 50
                  });
              });

              // إضافة دالة لضبط حجم الخط حسب طول النص
              function adjustFontSize(element, baseSize, maxChars, reductionFactor, minSize) {
                  var textLength = element.textContent.length;
                  var extraChars = textLength - maxChars;
                  var newSize = baseSize;
                  if (extraChars > 0) {
                      newSize = baseSize - (extraChars * reductionFactor);
                      if(newSize < minSize) newSize = minSize;
                  }
                  element.style.fontSize = newSize + "px";
              }

              // ضبط حجم خط اسم المنتج وسعره كما بملف الطباعة الفردية
              var nameDivs = document.querySelectorAll('[id^="productName_"]');
              nameDivs.forEach(function(div) {
                  if(div.innerHTML.indexOf('<br>') !== -1) {
                      adjustFontSize(div, 14, 20, 0.2, 10);
                  } else {
                      adjustFontSize(div, 16, 20, 0.3, 8);
                  }
              });

              window.print();
              window.close();
          });
        </script>
      </body>
      </html>
    `);

    printWindow.document.close();
}