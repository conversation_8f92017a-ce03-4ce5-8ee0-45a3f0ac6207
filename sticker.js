// Commented out the media query that hides the stickerPreview during print
// (function() {
//     var style = document.createElement('style');
//     style.innerHTML = '@media print { #stickerPreview { display: none; } }';
//     document.head.appendChild(style);
// })();

// Add global variable for tracking the print window
var printWindowRef = null;

// Add helper function to adjust font size dynamically based on text length.
function adjustFontSize(element, baseSize = 16, maxChars = 20, reductionFactor = 0.3, minSize = 8) {
    var extraChars = element.textContent.length - maxChars;
    var newSize = baseSize;
    if (extraChars > 0) {
        newSize = baseSize - (extraChars * reductionFactor);
        newSize = Math.max(newSize, minSize);
    }
    element.style.fontSize = newSize + "px";
}

function openStickerModal(itemName, barcodeValue, priceValue, qrValue = "") {
    // Automatically insert a line break between (FREM) and (OLED)
    itemName = itemName.replace("(FREM) (OLED)", "(FREM)<br>(OLED)");
    // Automatically break long names into two lines without altering the text
    if (itemName.replace(/<br>/g, "").length > 20) { // length without considering <br>
        var mid = Math.floor(itemName.replace(/<br>/g, "").length / 2);
        var idx = itemName.indexOf(" ", mid);
        if (idx === -1) idx = mid;
        itemName = itemName.substring(0, idx) + "<br>" + itemName.substring(idx + 1);
    }

    // لو لم يتم تمرير قيمة QR، نستخدم نفس قيمة الباركود
    if (!qrValue) {
        qrValue = barcodeValue;
    }

    var label = document.getElementById("stickerItemLabel");
    if (!label) {
        console.error("لم يتم العثور على العنصر stickerItemLabel.");
        return;
    }

    // تحضير السعر لعرضه إن وجد
    let priceHTML = "";
    if (priceValue.trim() !== "") {
        // Changed to use JOD instead of دينار and omit the phrase "سعر دينار" even if no price
        priceHTML = `${priceValue} JOD`;
    }

    // Set the background image to 1_6.png
    var backgroundUrl = "/Eyad.png";
    // بناء الـ HTML مع الخلفية وترتيب العناصر
    label.className = "sticker-label";
    label.innerHTML = `
      <div id="stickerContent" class="sticker-content"
           style="
             width: 100%;
             height: 100%;
             position: relative;
             background: url('${backgroundUrl}') no-repeat center center;
             background-size: cover;
             box-sizing: border-box;
           ">

        <!-- رمز QR في أعلى اليمين -->
        <div id="topRightQRCode" style="
             position: absolute;
             top: 2mm;
             right: 2mm;
        "></div>

        <!-- اسم العلامة (إن رغبت بإبقائه) في الأعلى وسط -->
        <div style="
             position: absolute;
             top: 2mm;
             left: 50%;
             transform: translateX(-50%);
             font-weight: bold;
             font-size: 14px;
             color: black;
        ">
          AN NAJJAR
        </div>

        <!-- اسم الصنف في المنتصف فوق الخط الأسود (حسب الخلفية) -->
        <div id="stickerProductName"
             style="
               position: absolute;
               top: 10mm; /* عدل القيم حسب موضع الخط في الخلفية */
               left: 50%;
               transform: translateX(-50%);
               font-size: 12px;
               font-weight: bold;
               color: black;
               text-align: center;
               width: 70%;               /* لتحديد المساحة وعدم التداخل مع QR */
               white-space: normal;             /* allow wrapping */
               word-wrap: break-word;
               display: -webkit-box;
               -webkit-box-orient: vertical;
               -webkit-line-clamp: 2;            /* maximum 2 lines */
               overflow: hidden;
             ">
          ${itemName}
        </div>

        <!-- السعر في المنتصف أسفل اسم الصنف وفوق الخط الأسود -->
        <div id="productDetails"
             style="
               position: absolute;
               top: 15mm; /* عدل القيم حسب موضع الخط في الخلفية */
               left: 50%;
               transform: translateX(-50%);
               font-size: 12px;
               font-weight: bold;
               color: black;
               text-align: center;
             ">
          ${priceHTML}
        </div>

        <!-- الباركود تحت الخط الأسود (حسب الخلفية) -->
        <div style="
     position: absolute;
     bottom: 5mm; 
     left: 50%;
     transform: translateX(-50%);
     text-align: center;
     width: 100%; /* تأكيد امتداد العرض لتوسيط المحتوى */
     display: flex;
     justify-content: center;
     align-items: center;
">

          <svg id="stickerSVG"></svg>
          <div id="barcodeNumber"
               style="
                 font-size: 14px;
                 font-family: 'Arial, sans-serif';
                 font-weight: bold;
                 margin-top: 2px;
                 color: #000;
               ">
          </div>
        </div>
      </div>
    `;

    // ضبط حجم خط اسم الصنف ديناميكياً باستخدام الدالة المساعدة
    var productNameElem = document.getElementById("stickerProductName");
    if (itemName.indexOf("<br>") !== -1) {
        // If the name spans two lines, use a smaller base font size (e.g., 10) and lower minimum (e.g., 6)
        adjustFontSize(productNameElem, 10, 20, 0.3, 6);
    } else {
        adjustFontSize(productNameElem, 16, 20, 0.3, 8);
    }

    // حفظ قيمة الباركود في نص مخفي
    var barcodeTextElem = document.getElementById("actualBarcode");
    barcodeTextElem.textContent = barcodeValue;

    // توليد الباركود باستخدام JsBarcode
    var svgElem = document.getElementById("stickerSVG");
    if (svgElem) {
        svgElem.innerHTML = "";
        let calcWidth = 2;
        if (barcodeValue.length > 10) {
            calcWidth = Math.max(2 * (10 / barcodeValue.length), 0.5);
        }
        let newWidth = calcWidth * 0.8; // تصغير بسيط للعرض إذا كان الباركود طويل
        JsBarcode("#stickerSVG", barcodeValue, {
            format: "CODE128",
            lineColor: "#000",
            width: newWidth,
            height: 50,
            displayValue: true,
            fontOptions: "bold",
            fontSize: 14,
            textMargin: 5
        });
    }

    // عرض رقم الباركود نصياً
    var barcodeNumberElem = document.getElementById("barcodeNumber");
    if (barcodeNumberElem) {
        barcodeNumberElem.textContent = barcodeValue;
    }

    // توليد رمز QR في أعلى اليمين
    var topRightQR = document.getElementById("topRightQRCode");
    if (topRightQR) {
        topRightQR.innerHTML = "";
        new QRCode(topRightQR, {
            text: qrValue,
            width: 50,
            height: 50
        });
    }

    // ضبط أبعاد الملصق
    var sticker = document.getElementById("stickerPreview");
    if (sticker) {
        sticker.style.width = "60mm"; // updated width
        sticker.style.height = "35mm"; // updated height
        sticker.style.position = "relative";
    }

}

function printSticker() {
    // منع فتح نافذة طباعة ثانية إذا كانت الأولى مفتوحة
    if (printWindowRef && !printWindowRef.closed) {
        printWindowRef.focus();
        return;
    }

    var sticker = document.getElementById("stickerPreview");
    if (!sticker) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لم يتم العثور على معاينة الاستيكر'
        });
        return;
    }

    var barcodeValue = document.getElementById("actualBarcode")?.textContent || "";
    if (!barcodeValue) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لم يتم العثور على الباركود'
        });
        return;
    }

    // Replace textContent with innerHTML to preserve any <br> tags for product name splitting
    var productName = document.getElementById("stickerProductName")?.innerHTML || "";
    var productPrice = document.getElementById("productDetails")?.textContent || "";

    // حساب عرض الباركود
    let calcWidth = 2;
    if (barcodeValue.length > 10) {
        calcWidth = Math.max(2 * (10 / barcodeValue.length), 0.5);
    }
    let newWidth = calcWidth * 0.8;

    // فتح نافذة طباعة وتخزين مرجعها
    printWindowRef = window.open("", "", "height=400,width=400");
    if (!printWindowRef) {
        Swal.fire({
            icon: 'error',
            title: 'خطأ',
            text: 'لا يمكن فتح نافذة الطباعة. يرجى إلغاء حظر النوافذ المنبثقة.'
        });
        return;
    }

    printWindowRef.document.write("<html><head><title>Sticker</title>");
    printWindowRef.document.write("<style>");
    printWindowRef.document.write("@page { size:60mm 35mm; margin: 0; }"); // updated page size
    printWindowRef.document.write("body { margin: 0; padding: 0; }");

    // تنسيق خلفية الملصق
    printWindowRef.document.write(".sticker-print {");
    printWindowRef.document.write("  position: relative;");
    printWindowRef.document.write("  width: 60mm;"); // updated width
    printWindowRef.document.write("  height: 35mm;"); // updated height
    // إذا أردت الخلفية الافتراضية:
    printWindowRef.document.write("  background: " + (sticker.style.background || "url('/Eyad.png') no-repeat center center") + ";");
    printWindowRef.document.write("  background-size: cover;");
    printWindowRef.document.write("  overflow: hidden;");
    printWindowRef.document.write("  font-family: sans-serif;");
    printWindowRef.document.write("}");

    // تنسيق مكان الـ QR
    printWindowRef.document.write(".qrTopRight {");
    printWindowRef.document.write("  position: absolute;");
    printWindowRef.document.write("  top: 2mm;");
    printWindowRef.document.write("  right: 2mm;");
    printWindowRef.document.write("}");

    // حاوية معلومات المنتج (الاسم + السعر)
    printWindowRef.document.write(".productInfo {");
    printWindowRef.document.write("  position: absolute;");
    printWindowRef.document.write("  top: 5mm;");
    printWindowRef.document.write("  left: 52%;");
    printWindowRef.document.write("  transform: translateX(-50%);");
    printWindowRef.document.write("  width: 90%;");
    printWindowRef.document.write("  text-align: center;");
    printWindowRef.document.write("}");

    // اسم الصنف مع تمكين التفافه على سطرين
    printWindowRef.document.write(".productName {");
    printWindowRef.document.write("  display: -webkit-box;");
    printWindowRef.document.write("  -webkit-box-orient: vertical;");
    printWindowRef.document.write("  -webkit-line-clamp: 2;"); // limits to 2 lines
    printWindowRef.document.write("  overflow: hidden;");
    printWindowRef.document.write("  width: 90%;");
    printWindowRef.document.write("  white-space: normal;");
    printWindowRef.document.write("  word-wrap: break-word;");
    printWindowRef.document.write("  font-size: 11px;");
    printWindowRef.document.write("  font-weight: bold;");
    printWindowRef.document.write("  margin: 0 auto;");
    printWindowRef.document.write("  text-align: center;");
    printWindowRef.document.write("}");

    // السعر
    printWindowRef.document.write(".productPrice {");
    printWindowRef.document.write("  margin-top: 2mm;");
    printWindowRef.document.write("  font-size: 11px;");
    printWindowRef.document.write("  font-weight: bold;");
    printWindowRef.document.write("  color: red;");
    printWindowRef.document.write("}");

    // تعديل: تحديث تنسيق الحاوية التي تحتوي على الباركود لتوسيطه أفقيًا 
    printWindowRef.document.write(".barcodeContainer {");
    printWindowRef.document.write("  position: absolute;");
    printWindowRef.document.write("  bottom: -15mm; /* تعديل المسافة من الأسفل حسب الحاجة */");
    printWindowRef.document.write("  left: 50%;");
    printWindowRef.document.write("  transform: translateX(-50%);");
    printWindowRef.document.write("  width: 90%;");
    printWindowRef.document.write("  height: 25mm;");
    printWindowRef.document.write("  text-align: center; /* لضمان توسيط المحتوى أفقيًا */");
    printWindowRef.document.write("}");

    // إضافة تنسيق لـ SVG داخل الحاوية
    printWindowRef.document.write("svg { display: inline-block; margin: 0 auto; }");

    printWindowRef.document.write("</style>");
    printWindowRef.document.write("</head><body>");

    // HTML الملصق
    printWindowRef.document.write('<div class="sticker-print">');
    printWindowRef.document.write('<div class="qrTopRight" id="printQRCode"></div>');
    printWindowRef.document.write('<div class="productInfo">');
    printWindowRef.document.write('<div class="productName" id="printProductName">' + productName + '</div>');
    printWindowRef.document.write('<div class="productPrice" id="printProductPrice">' + productPrice + '</div>');
    printWindowRef.document.write('</div>');
    printWindowRef.document.write('<div class="barcodeContainer"><svg id="printBarcode"></svg></div>');
    printWindowRef.document.write('</div>');

    // مكتبات JsBarcode وQRCode
    printWindowRef.document.write("<script src='https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js'><\/script>");
    printWindowRef.document.write("<script src='https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js'><\/script>");

    // سكربت الطباعة
    printWindowRef.document.write("<script>");
    printWindowRef.document.write("document.addEventListener('DOMContentLoaded', function() {");
    printWindowRef.document.write("  JsBarcode('#printBarcode', '" + barcodeValue + "', {");
    printWindowRef.document.write("    format: 'CODE128',");
    printWindowRef.document.write("    lineColor: '#000',");
    printWindowRef.document.write("    width: " + newWidth + ",");
    printWindowRef.document.write("    height: 18,");
    printWindowRef.document.write("    displayValue: true,");
    printWindowRef.document.write("    fontOptions: 'bold',");
    printWindowRef.document.write("    fontSize: 14,");
    printWindowRef.document.write("    margin: 0");
    printWindowRef.document.write("  });");
    printWindowRef.document.write("  new QRCode(document.getElementById('printQRCode'), {");
    printWindowRef.document.write("    text: '" + barcodeValue + "',");
    printWindowRef.document.write("    width: 50,");
    printWindowRef.document.write("    height: 50");
    printWindowRef.document.write("  });");
    printWindowRef.document.write("  window.print();");

    printWindowRef.document.write("});");
    printWindowRef.document.write("<\/script>");

    printWindowRef.document.write("</body></html>");
    printWindowRef.document.close();
}


function closeStickerModal() {
    var modal = document.getElementById("stickerModal");
    if (modal) {
        modal.style.display = "none";
    }
}

// دالة لاختيار السعر من عدة خيارات وطباعته بدون عرض الاستيكر
function choosePriceAndPrint(itemName, barcodeValue, price1, price2, price3, price4) {
    Swal.fire({
        title: 'اختر السعر',
        input: 'select',
        inputOptions: {
            '0': 'بدون سعر',
            '1': 'السعر الأول: ' + price1,
            '2': 'السعر الثاني: ' + price2,
            '3': 'السعر الثالث: ' + price3,
            '4': 'السعر الرابع: ' + price4
        },
        inputPlaceholder: 'اختر السعر',
        showCancelButton: true
    }).then((result) => {
        if (result.isConfirmed && result.value !== undefined) {
            let selectedPrice = "";
            switch (result.value) {
                case '0':
                    selectedPrice = ""; // no price option
                    break;
                case '1':
                    selectedPrice = price1;
                    break;
                case '2':
                    selectedPrice = price2;
                    break;
                case '3':
                    selectedPrice = price3;
                    break;
                case '4':
                    selectedPrice = price4;
                    break;
            }
            // Call sticker generation and directly print without showing the modal
            openStickerModal(itemName, barcodeValue, selectedPrice);
            printSticker();
        }
    });
}

// عند تحميل الصفحة، نجعل الدوال متاحة عالمياً
document.addEventListener("DOMContentLoaded", function() {
    window.openStickerModal = openStickerModal;
    window.printSticker = printSticker;
    window.closeStickerModal = closeStickerModal;
    window.choosePriceAndPrint = choosePriceAndPrint;

    if (typeof QRCode === "undefined") {
        var script = document.createElement('script');
        script.src = "https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js";
        document.head.appendChild(script);
    }
});