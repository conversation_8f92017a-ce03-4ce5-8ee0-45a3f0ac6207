<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار خلفية الاستيكر</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .sticker-test {
            width: 60mm;
            height: 35mm;
            border: 2px dashed #ccc;
            margin: 10px;
            display: inline-block;
            position: relative;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        .sticker-content {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: black;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        
        .path-test {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .success {
            color: green;
        }
        
        .error {
            color: red;
        }
        
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار خلفية الاستيكر</h1>
        
        <div class="test-section">
            <h3>اختبار المسارات المختلفة</h3>
            
            <div class="path-test">
                <strong>المسار 1:</strong> <code>Eyad.png</code> (مسار نسبي)
                <div class="sticker-test" style="background-image: url('Eyad.png');">
                    <div class="sticker-content">اختبار 1</div>
                </div>
            </div>
            
            <div class="path-test">
                <strong>المسار 2:</strong> <code>./Eyad.png</code> (مسار نسبي مع ./)
                <div class="sticker-test" style="background-image: url('./Eyad.png');">
                    <div class="sticker-content">اختبار 2</div>
                </div>
            </div>
            
            <div class="path-test">
                <strong>المسار 3:</strong> <code>/Eyad.png</code> (مسار مطلق من الجذر)
                <div class="sticker-test" style="background-image: url('/Eyad.png');">
                    <div class="sticker-content">اختبار 3</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>معلومات النظام</h3>
            <div id="systemInfo"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار تحميل الإعدادات</h3>
            <button onclick="testLoadSettings()">تحميل الإعدادات</button>
            <div id="settingsResult"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار حفظ الإعدادات</h3>
            <button onclick="testSaveSettings()">حفظ إعدادات تجريبية</button>
            <div id="saveResult"></div>
        </div>
    </div>
    
    <script>
        // عرض معلومات النظام
        document.addEventListener('DOMContentLoaded', function() {
            const systemInfo = document.getElementById('systemInfo');
            systemInfo.innerHTML = `
                <div class="info"><strong>URL الحالي:</strong> ${window.location.href}</div>
                <div class="info"><strong>مسار الصفحة:</strong> ${window.location.pathname}</div>
                <div class="info"><strong>المجلد الأساسي:</strong> ${window.location.origin}</div>
            `;
        });
        
        // اختبار تحميل الإعدادات
        function testLoadSettings() {
            const resultDiv = document.getElementById('settingsResult');
            resultDiv.innerHTML = '<div class="info">جاري التحميل...</div>';
            
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_settings'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">تم تحميل الإعدادات بنجاح!</div>
                        <div class="path-test">
                            <strong>صورة الخلفية:</strong> <code>${data.settings.background_image}</code>
                        </div>
                        <pre>${JSON.stringify(data.settings, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">فشل في تحميل الإعدادات: ${JSON.stringify(data)}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">خطأ في الشبكة: ${error.message}</div>`;
            });
        }
        
        // اختبار حفظ الإعدادات
        function testSaveSettings() {
            const resultDiv = document.getElementById('saveResult');
            resultDiv.innerHTML = '<div class="info">جاري الحفظ...</div>';
            
            const testSettings = {
                background_image: 'Eyad.png',
                width: '60mm',
                height: '35mm',
                brand_name: 'AN NAJJAR TEST'
            };
            
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_settings&settings=${encodeURIComponent(JSON.stringify(testSettings))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">تم حفظ الإعدادات بنجاح!</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">فشل في حفظ الإعدادات: ${JSON.stringify(data)}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">خطأ في الشبكة: ${error.message}</div>`;
            });
        }
        
        // اختبار تحميل الصور
        function testImageLoad() {
            const images = ['Eyad.png', './Eyad.png', '/Eyad.png'];
            
            images.forEach((imagePath, index) => {
                const img = new Image();
                img.onload = function() {
                    console.log(`✅ تم تحميل الصورة بنجاح: ${imagePath}`);
                };
                img.onerror = function() {
                    console.log(`❌ فشل في تحميل الصورة: ${imagePath}`);
                };
                img.src = imagePath;
            });
        }
        
        // تشغيل اختبار الصور عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', testImageLoad);
    </script>
</body>
</html>
