<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إرسال رسائل واتساب</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366, #128C7E);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: #25D366;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .form-container {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, textarea:focus {
            outline: none;
            border-color: #25D366;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background: #25D366;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }
        
        .btn:hover {
            background: #128C7E;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .note {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25D366;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 اختبار إرسال رسائل واتساب</h1>
            <p>WAPilot API Integration Test</p>
        </div>
        
        <div class="form-container">
            <div class="note">
                <strong>ملاحظة:</strong> Instance ID موجود مسبقاً. تحتاج فقط لإدخال Chat ID (رقم هاتف المستقبل) بصيغة: رقم_الهاتف@c.us
            </div>
            
            <form id="whatsappForm">
                <div class="form-group">
                    <label for="instance_id">Instance ID:</label>
                    <input type="text" id="instance_id" name="instance_id" placeholder="أدخل instance_id الخاص بك" value="instance1678" required>
                    <small style="color: #666;">Instance ID الخاص بحسابك في WAPilot</small>
                </div>
                
                <div class="form-group">
                    <label for="chat_id">Chat ID (رقم الهاتف المستقبل):</label>
                    <input type="text" id="chat_id" name="chat_id" placeholder="مثال: <EMAIL>" required>
                    <small style="color: #666;">رقم هاتف الشخص المراد إرساله له بصيغة: رقم_الهاتف@c.us</small>
                </div>
                
                <div class="form-group">
                    <label for="message">الرسالة:</label>
                    <textarea id="message" name="message" placeholder="اكتب رسالتك هنا..." required>مرحباً! هذه رسالة تجريبية من نظام إدارة المشتريات 📦</textarea>
                </div>
                
                <button type="submit" class="btn">📤 إرسال الرسالة</button>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري الإرسال...</p>
                </div>
            </form>
            
            <div id="result" class="result"></div>
        </div>
    </div>

    <script>
        document.getElementById('whatsappForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const instanceId = document.getElementById('instance_id').value;
            const chatId = document.getElementById('chat_id').value;
            const message = document.getElementById('message').value;
            const resultDiv = document.getElementById('result');
            const loadingDiv = document.getElementById('loading');
            
            // إخفاء النتائج السابقة وإظهار التحميل
            resultDiv.style.display = 'none';
            loadingDiv.style.display = 'block';
            
            try {
                const response = await fetch('send_whatsapp_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        instance_id: instanceId,
                        chat_id: chatId,
                        text: message
                    })
                });
                
                const data = await response.json();
                
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ تم إرسال الرسالة بنجاح!</h3>
                        <p><strong>Message ID:</strong> ${data.data.message_id || 'غير متوفر'}</p>
                        <p><strong>الحالة:</strong> ${data.data.status || 'تم الإرسال'}</p>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ فشل في إرسال الرسالة</h3>
                        <p><strong>الخطأ:</strong> ${data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                loadingDiv.style.display = 'none';
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ خطأ في الاتصال</h3>
                    <p>${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>