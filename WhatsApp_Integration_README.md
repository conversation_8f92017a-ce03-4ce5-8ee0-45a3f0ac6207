# 📱 دليل تكامل واتساب - WAPilot API

## 🔧 إعدادات الاتصال

### معلومات الحساب
- **API Provider:** WAPilot
- **Base URL:** `https://wapilot.net/api/v1`
- **Token:** `SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH`
- **Instance ID:** `instance1678`

---

## 📋 طريقة الإرسال

### 1. **Request URL**
```
POST https://wapilot.net/api/v1/{instance_id}/send-message
```

### 2. **Headers**
```json
{
    "Content-Type": "application/json"
}
```

### 3. **Request Body**
```json
{
    "token": "SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH",
    "chat_id": "<EMAIL>",
    "text": "نص الرسالة هنا"
}
```

---

## 🚀 مثال عملي

### cURL Command
```bash
curl -X POST "https://wapilot.net/api/v1/instance1678/send-message" \
-H "Content-Type: application/json" \
-d '{
    "token": "SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH",
    "chat_id": "<EMAIL>",
    "text": "مرحباً! هذه رسالة تجريبية"
}'
```

### PHP Example
```php
<?php
function sendWhatsAppMessage($chatId, $message) {
    $url = "https://wapilot.net/api/v1/instance1678/send-message";
    
    $data = array(
        'token' => 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH',
        'chat_id' => $chatId,
        'text' => $message
    );
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json'
    ));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return json_decode($response, true);
}

// الاستخدام
$result = sendWhatsAppMessage('<EMAIL>', 'مرحباً!');
?>
```

### JavaScript Example
```javascript
async function sendWhatsAppMessage(chatId, message) {
    const url = 'https://wapilot.net/api/v1/instance1678/send-message';
    
    const data = {
        token: 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH',
        chat_id: chatId,
        text: message
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Error:', error);
        return null;
    }
}

// الاستخدام
sendWhatsAppMessage('<EMAIL>', 'مرحباً!');
```

---

## 📝 تنسيق Chat ID

### القواعد المهمة:
1. **تنسيق رقم الهاتف:** `رقم_الهاتف@c.us`
2. **مع كود البلد:** يجب تضمين كود البلد (مصر: 20)
3. **بدون علامة +:** لا تستخدم علامة + في البداية

### أمثلة صحيحة:
```
<EMAIL>    ✅ رقم مصري
<EMAIL>    ✅ رقم سعودي
<EMAIL>    ✅ رقم إماراتي
```

### أمثلة خاطئة:
```
+<EMAIL>   ❌ لا تستخدم +
<EMAIL>     ❌ بدون كود البلد
201234567890         ❌ بدون @c.us
```

---

## 🔧 الملفات المطلوبة في المشروع

### 1. **test_whatsapp.php** - صفحة الاختبار
- واجهة مستخدم لاختبار إرسال الرسائل
- تحتوي على نموذج إدخال البيانات
- عرض النتائج والأخطاء

### 2. **send_whatsapp_message.php** - معالج الإرسال
- يتعامل مع طلبات AJAX
- يحتوي على منطق إرسال الرسائل
- معالجة الأخطاء والاستجابات

### 3. **whatsapp_helper.php** - مكتبة الدوال المساعدة
- دوال جاهزة لأنواع مختلفة من الرسائل
- تنسيق أرقام الهواتف
- تسجيل العمليات

---

## 📱 أنواع الرسائل المدعومة

### 1. **رسالة ترحيب**
```php
$result = WhatsAppSender::sendWelcomeMessage(
    'instance1678', 
    '<EMAIL>', 
    'أحمد محمد'
);
```

### 2. **إشعار طلب جديد**
```php
$orderData = [
    'order_id' => '12345',
    'customer_name' => 'أحمد محمد',
    'total_amount' => '500',
    'order_link' => 'https://example.com/order/12345'
];

$result = WhatsAppSender::sendNewOrderNotification(
    'instance1678', 
    '<EMAIL>', 
    $orderData
);
```

### 3. **تأكيد الطلب**
```php
$orderData = [
    'order_id' => '12345',
    'total_amount' => '500',
    'delivery_date' => '2024-01-15'
];

$result = WhatsAppSender::sendOrderConfirmation(
    'instance1678', 
    '<EMAIL>', 
    $orderData
);
```

### 4. **تحديث حالة الطلب**
```php
$orderData = [
    'order_id' => '12345',
    'status' => 'shipped',
    'notes' => 'تم الشحن عبر شركة النقل'
];

$result = WhatsAppSender::sendOrderStatusUpdate(
    'instance1678', 
    '<EMAIL>', 
    $orderData
);
```

### 5. **تذكير بالدفع**
```php
$orderData = [
    'order_id' => '12345',
    'amount_due' => '500',
    'due_date' => '2024-01-20'
];

$result = WhatsAppSender::sendPaymentReminder(
    'instance1678', 
    '<EMAIL>', 
    $orderData
);
```

---

## ⚙️ إعدادات المشروع

### تحديث الإعدادات في `whatsapp_helper.php`:
```php
class WhatsAppConfig {
    const TOKEN = 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH';
    const BASE_URL = 'https://wapilot.net/api/v1';
    const INSTANCE_ID = 'instance1678';
    const MAX_MESSAGE_LENGTH = 4096;
}
```

### تحديث الإعدادات في `send_whatsapp_message.php`:
```php
define('WHATSAPP_TOKEN', 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH');
define('INSTANCE_ID', 'instance1678');
```

---

## 🧪 اختبار النظام

### 1. **افتح صفحة الاختبار:**
```
http://localhost/annajjar_v2/test_whatsapp.php
```

### 2. **أدخل البيانات:**
- **Instance ID:** `instance1678` (موجود مسبقاً)
- **Chat ID:** رقم الهاتف المستقبل (مثال: `<EMAIL>`)
- **الرسالة:** اكتب رسالتك

### 3. **اضغط إرسال واختبر النتيجة**

---

## 📊 معالجة الاستجابات

### استجابة ناجحة:
```json
{
    "success": true,
    "data": {
        "message_id": "msg_12345",
        "status": "sent",
        "timestamp": "2024-01-01T12:00:00Z"
    },
    "http_code": 200
}
```

### استجابة فاشلة:
```json
{
    "success": false,
    "error": "Invalid chat_id format",
    "http_code": 400
}
```

---

## 🔒 أمان وحماية

### 1. **حماية التوكن:**
- لا تعرض التوكن في الكود العام
- استخدم متغيرات البيئة (.env)
- قم بتشفير التوكن في قاعدة البيانات

### 2. **التحقق من البيانات:**
- تحقق من صحة Chat ID
- تحقق من طول الرسالة (حد أقصى 4096 حرف)
- تنظيف البيانات المدخلة

### 3. **تسجيل العمليات:**
- احفظ لوج لكل عملية إرسال
- سجل الأخطاء والنجاحات
- راقب معدل الإرسال

---

## 🚨 استكشاف الأخطاء

### أخطاء شائعة:

1. **Invalid token**
   - تأكد من صحة التوكن
   - تحقق من انتهاء صلاحية التوكن

2. **Invalid chat_id**
   - تأكد من تنسيق Chat ID الصحيح
   - تحقق من وجود @c.us في النهاية

3. **Message too long**
   - الحد الأقصى 4096 حرف
   - قسم الرسائل الطويلة

4. **Rate limit exceeded**
   - قلل معدل الإرسال
   - أضف تأخير بين الرسائل

---

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع وثائق WAPilot API
2. تحقق من لوج الأخطاء
3. اختبر الاتصال بـ API مباشرة
4. تأكد من صحة الإعدادات

---

## 📈 تطوير مستقبلي

### ميزات مقترحة:
- [ ] إرسال الصور والملفات
- [ ] إرسال رسائل جماعية
- [ ] جدولة الرسائل
- [ ] قوالب رسائل محفوظة
- [ ] تقارير الإرسال
- [ ] واجهة إدارة متقدمة

---

**تم إنشاء هذا الدليل في:** `r date()`  
**الإصدار:** 1.0  
**المطور:** فريق تطوير نظام إدارة المشتريات