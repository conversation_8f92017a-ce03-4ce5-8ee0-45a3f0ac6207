/**
 * أنماط نظام الاستيكر الموحد
 * Unified Sticker System Styles
 */

/* ===== متغيرات CSS ===== */
:root {
    --sticker-width: 60mm;
    --sticker-height: 35mm;
    --sticker-bg-color: #ffffff;
    --sticker-text-color: #000000;
    --sticker-border-color: #cccccc;
    --sticker-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --sticker-font-family: 'Cairo', 'Arial', sans-serif;
}

/* ===== أنماط الاستيكر الأساسية ===== */
.sticker-preview {
    width: var(--sticker-width);
    height: var(--sticker-height);
    border: 1px dashed var(--sticker-border-color);
    box-shadow: var(--sticker-shadow);
    margin: 0 auto;
    position: relative;
    background: var(--sticker-bg-color);
    font-family: var(--sticker-font-family);
    overflow: hidden;
    box-sizing: border-box;
}

.sticker-content {
    width: 100%;
    height: 100%;
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* ===== أنماط النص ===== */
.sticker-brand-name {
    position: absolute;
    top: 2mm;
    left: 50%;
    transform: translateX(-50%);
    font-weight: bold;
    font-size: 14px;
    color: var(--sticker-text-color);
    text-align: center;
}

.sticker-product-name {
    position: absolute;
    top: 10mm;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: var(--sticker-text-color);
    text-align: center;
    width: 70%;
    white-space: normal;
    word-wrap: break-word;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    line-height: 1.2;
}

.sticker-price {
    position: absolute;
    top: 15mm;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: var(--sticker-text-color);
    text-align: center;
}

/* ===== أنماط الباركود ===== */
.sticker-barcode-container {
    position: absolute;
    bottom: 5mm;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.sticker-barcode-number {
    font-size: 14px;
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    margin-top: 2px;
    color: var(--sticker-text-color);
}

/* ===== أنماط رمز QR ===== */
.sticker-qr-code {
    position: absolute;
    top: 2mm;
    right: 2mm;
}

/* ===== أنماط المودال ===== */
.sticker-modal {
    display: none;
    position: fixed;
    z-index: 1500;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.sticker-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: none;
    border-radius: 10px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
}

.sticker-modal-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    top: 10px;
    right: 15px;
    transition: color 0.3s ease;
}

.sticker-modal-close:hover,
.sticker-modal-close:focus {
    color: #000;
}

.sticker-modal-title {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: bold;
}

/* ===== أنماط أزرار الطباعة ===== */
.sticker-print-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    margin-top: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.sticker-print-btn:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.sticker-print-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

/* ===== أنماط أيقونة الطابعة ===== */
.sticker-printer-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.sticker-printer-icon:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* ===== أنماط الطباعة ===== */
@media print {
    .sticker-print {
        width: var(--sticker-width);
        height: var(--sticker-height);
        margin: 0;
        padding: 0;
        page-break-inside: avoid;
        page-break-after: always;
    }
    
    .sticker-no-print {
        display: none !important;
    }
}

@page {
    size: var(--sticker-width) var(--sticker-height);
    margin: 0;
}

/* ===== أنماط متجاوبة ===== */
@media (max-width: 768px) {
    .sticker-modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 15px;
    }
    
    .sticker-preview {
        transform: scale(0.8);
        transform-origin: center;
    }
}

@media (max-width: 480px) {
    .sticker-modal-content {
        width: 98%;
        margin: 15% auto;
        padding: 10px;
    }
    
    .sticker-preview {
        transform: scale(0.7);
    }
    
    .sticker-print-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}

/* ===== أنماط الرسوم المتحركة ===== */
@keyframes stickerFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.sticker-animate-in {
    animation: stickerFadeIn 0.3s ease-out;
}

@keyframes stickerPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.sticker-pulse {
    animation: stickerPulse 0.6s ease-in-out;
}

/* ===== أنماط الحالات الخاصة ===== */
.sticker-loading {
    position: relative;
}

.sticker-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.sticker-error {
    border-color: #e74c3c;
    background-color: #fdf2f2;
}

.sticker-success {
    border-color: #27ae60;
    background-color: #f2fdf5;
}

/* ===== أنماط الوضع المظلم ===== */
[data-theme="dark"] .sticker-modal-content {
    background-color: #2c3e50;
    color: #ecf0f1;
}

[data-theme="dark"] .sticker-modal-close {
    color: #bdc3c7;
}

[data-theme="dark"] .sticker-modal-close:hover {
    color: #ecf0f1;
}

[data-theme="dark"] .sticker-preview {
    border-color: #34495e;
    background-color: #34495e;
}

/* ===== أنماط إضافية للتحسين ===== */
.sticker-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 20px;
}

.sticker-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.sticker-info {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
}

[data-theme="dark"] .sticker-info {
    background: #34495e;
    color: #bdc3c7;
}
