<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التوكن الخاص بك
define('WHATSAPP_TOKEN', 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH');

function sendWhatsAppMessage($instanceId, $chatId, $message) {
    // رابط API
    $url = "https://wapilot.net/api/v1/{$instanceId}/send-message";
    
    // البيانات المرسلة
    $data = array(
        'token' => WHATSAPP_TOKEN,
        'chat_id' => $chatId,
        'text' => $message
    );
    
    // إعداد cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Accept: application/json'
    ));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // تنفيذ الطلب
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // التحقق من وجود خطأ في cURL
    if ($error) {
        return array(
            'success' => false,
            'error' => 'cURL Error: ' . $error,
            'http_code' => $httpCode
        );
    }
    
    // فك تشفير الاستجابة
    $responseData = json_decode($response, true);
    
    // التحقق من نجاح العملية
    if ($httpCode == 200) {
        return array(
            'success' => true,
            'data' => $responseData,
            'http_code' => $httpCode,
            'raw_response' => $response
        );
    } else {
        return array(
            'success' => false,
            'error' => 'HTTP Error: ' . $httpCode,
            'data' => $responseData,
            'http_code' => $httpCode,
            'raw_response' => $response
        );
    }
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(array(
        'success' => false,
        'error' => 'Method not allowed. Use POST.'
    ));
    exit;
}

// قراءة البيانات المرسلة
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// التحقق من صحة البيانات
if (!$data) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'error' => 'Invalid JSON data'
    ));
    exit;
}

// التحقق من وجود الحقول المطلوبة
$requiredFields = ['instance_id', 'chat_id', 'text'];
foreach ($requiredFields as $field) {
    if (!isset($data[$field]) || empty($data[$field])) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'error' => "Missing required field: {$field}"
        ));
        exit;
    }
}

// تنظيف البيانات
$instanceId = trim($data['instance_id']);
$chatId = trim($data['chat_id']);
$message = trim($data['text']);

// التحقق من طول الرسالة (الحد الأقصى 4096 حرف)
if (strlen($message) > 4096) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'error' => 'Message too long. Maximum 4096 characters allowed.'
    ));
    exit;
}

// إرسال الرسالة
try {
    $result = sendWhatsAppMessage($instanceId, $chatId, $message);
    
    // تسجيل العملية (اختياري)
    $logData = array(
        'timestamp' => date('Y-m-d H:i:s'),
        'instance_id' => $instanceId,
        'chat_id' => $chatId,
        'message_length' => strlen($message),
        'success' => $result['success'],
        'http_code' => $result['http_code']
    );
    
    // يمكنك حفظ اللوج في قاعدة البيانات أو ملف
    // file_put_contents('whatsapp_logs.txt', json_encode($logData) . "\n", FILE_APPEND);
    
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ));
}
?>