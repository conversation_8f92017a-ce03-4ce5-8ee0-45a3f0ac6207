table td {
    color: white;
}
.status-frame {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
}
.status-frame.pending {
    background-color: #ff9800;
    color: white;
}
.status-frame.confirmed {
    background-color: #4caf50;
    color: white;
}
.status-frame.delayed {
    background-color: #f44336;
    color: white;
}
.collect-btn {
    background: url('icons/Paid_24.png') no-repeat center center;
    background-size: contain;
    width: 24px;
    height: 24px;
    border: none;
    cursor: pointer;
}
.search-bar {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.search-bar input,
.search-bar button {
    padding: 10px;
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid #ddd;
    background-color: #2b3a52;
    color: white;
}
.search-bar input[type="date"] {
    background-color: #2b3a52;
    color: white;
}
.search-bar input[type="date"]::before {
    content: attr(placeholder);
    color: #aaa;
    margin-right: 10px;
}
.search-bar button {
    background-color: #4caf50;
    color: white;
    border: none;
    cursor: pointer;
    margin-left: 10px;
}
.search-bar button.clear-btn {
    background-color: #f44336;
}
.search-bar button:hover {
    background-color: #45a049;
}
.search-bar button.clear-btn:hover {
    background-color: #e53935;
}
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.6);
}
.modal-content {
    background: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
}
.popup-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

.popup-message.success {
    background-color: #4caf50;
    color: #ffffff;
}

.confirmation-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    display: none;
    text-align: center;
}

.confirmation-popup p {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
}

.confirmation-popup button {
    padding: 10px 20px;
    margin: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.confirmation-popup .confirm {
    background-color: #4caf50;
    color: white;
}

.confirmation-popup .cancel {
    background-color: #f44336;
    color: white;
}
#sidebar ul li.branch {
padding: 10px 15px;
background-color: #2c3e50; /* لون خلفية مميز */
color: #ecf0f1;
border-radius: 5px;
margin: 10px 0;
}
.branch-container {
display: flex;
align-items: center;
}
.branch-icon {
margin-right: 8px;
}
.branch-text {
font-size: 1.1rem;
font-weight: bold;
}
.search-bar select {
padding: 10px;
font-size: 16px;
border-radius: 5px;
border: 1px solid #ddd;
background-color: #2b3a52;
color: white;
}
.search-bar select option {
background-color: #2b3a52;
color: white;
}

.printer-icon, .eye-icon, .delete-icon {
width: 24px;
height: 24px;
cursor: pointer;
vertical-align: middle;
}
.icon-spacing {
margin-right: 20px;
}
.add-invoice-btn {
background-color: #4caf50;
color: white;
padding: 10px 20px;
border: none;
border-radius: 5px;
cursor: pointer;
font-size: 16px;
margin-bottom: 20px;
}
.add-invoice-btn:hover {
background-color: #45a049;
}
.add-order-btn {
background-color: #4caf50;
color: white;
padding: 10px 20px;
border: none;
border-radius: 5px;
cursor: pointer;
font-size: 16px;
margin-bottom: 20px;
}
.add-order-btn:hover {
background-color: #45a049;
}
#userOrdersModal table tbody tr td {
color: black;
}
.summary-bar {
display: flex;
justify-content: space-between;
padding: 10px;
background-color: #f8f9fa;
border-radius: 5px;
margin-bottom: 20px;
}
.summary-item {
flex: 1;
text-align: center;
font-weight: bold;
color: #333;
}
.frame {
border: 1px solid #ccc;
padding: 10px;
border-radius: 5px;
background-color: #fff;
}
.fixed-navbar {
position: sticky;
bottom: 0;
left: 0;
width: 100%;
background-color: #2b3a53;
padding: 10px 0;
box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
text-align: center;
z-index: 1000;
}

.fixed-navbar .btn-danger {
margin: 0 auto;
}
/* نفس كود الاستايل لقسم البحث من purchase_invoices.php */
.search-container {
background-color: #2b3a52;  /* matching table background */
padding: 10px 15px;         /* reduced padding */
border: 1px solid #1a2537;
border-radius: 6px;
margin-bottom: 15px;
box-shadow: none;
}
.search-form {
display: flex;
flex-wrap: wrap;
gap: 10px;
justify-content: space-between;
}
.search-field {
flex: 1 1 160px;
display: flex;
flex-direction: column;
}
.search-field label {
font-size: 12px;
margin-bottom: 4px;
color: #fdb813;
}
.search-field input[type="text"],
.search-field input[type="date"] {
padding: 6px 8px;
border: 1px solid #ccc;
border-radius: 4px;
font-size: 12px;
transition: border-color 0.3s, box-shadow 0.3s;
}
.search-field input[type="text"]:focus,
.search-field input[type="date"]:focus {
border-color: #007bff;
outline: none;
box-shadow: 0 0 4px rgba(0,123,255,0.5);
}
/* Adjusted styles for the search section */
.search-container {
background-color: #2b3a52;
padding: 8px 12px; /* Reduced padding */
border: 1px solid #1a2537;
border-radius: 6px;
margin-bottom: 15px;
box-shadow: none;
}

.search-form {
display: flex;
flex-wrap: wrap;
gap: 8px; /* Reduced gap */
justify-content: space-between;
}

.search-field {
flex: 1 1 140px; /* Smaller width */
display: flex;
flex-direction: column;
}

.search-field label {
font-size: 11px; /* Smaller font size */
margin-bottom: 3px;
color: #fdb813;
}

.search-field input[type="text"],
.search-field input[type="date"],
.search-field select {
padding: 5px 7px; /* Reduced padding */
border: 1px solid #ccc;
border-radius: 4px;
font-size: 11px; /* Smaller font size */
transition: border-color 0.3s, box-shadow 0.3s;
}

.search-field input[type="text"]:focus,
.search-field input[type="date"]:focus,
.search-field select:focus {
border-color: #007bff;
outline: none;
box-shadow: 0 0 4px rgba(0, 123, 255, 0.5);
}

/* Custom styles for the buttons */
.search-field button {
padding: 6px 12px;
font-size: 12px;
border-radius: 4px;
border: none;
cursor: pointer;
transition: background-color 0.3s ease;
}

.search-field button:hover {
background-color: #45a049;
}

.search-field button.clear-btn {
background-color: #f44336;
color: white;

}

.search-field button.clear-btn:hover {
background-color: #e53935;
}

/* Custom styles for the status dropdown */
.search-field select {
background-color: #2b3a52;
color: white;
}

.search-field select option {
background-color: #2b3a52;
color: white;
}
.status-row {
display: flex;
gap: 10px;
align-items: center;
/* خلفية خفيفة لإبراز الصف */
background-color: #f1f1f1;
padding: 8px 15px;
border-radius: 5px;
}

/* تنسيق أزرار الحالة العامة */
.status-buttons {
display: flex;
flex-direction: column;
gap: 15px;
flex: 0 0 auto;
margin-top: 8px;
}

.status-buttons button {
padding: 8px 12px;
font-size: 14px;
background-color: #007bff;  /* لون أزرق مميز */
color: #fff;
border: none;
border-radius: 4px;
cursor: pointer;
transition: background-color 0.3s, transform 0.2s;
}

.status-buttons button:hover {
background-color: #0056b3;
transform: scale(1.03);
}

/* تنسيق أزرار الإجراءات الصغيرة (بعد حقل الحالة) */
.action-status-buttons {
margin-top: 8px;
display: flex;
gap: 6px;
}

.action-status-buttons button {
padding: 6px 10px;
font-size: 12px;
background-color: #28a745; /* لون أخضر مميز */
color: #fff;
border: none;
border-radius: 3px;
cursor: pointer;
transition: background-color 0.3s, transform 0.2s;
}

.action-status-buttons button:hover {
background-color: #218838;
transform: scale(1.05);
}
