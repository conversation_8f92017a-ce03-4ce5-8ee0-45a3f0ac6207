/* Dashboard Custom Styles - متناسق مع style_web.css */

/* استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');

/* متغيرات الألوان من style_web.css */
:root {
    --color-bg: linear-gradient(to right, #f5f7fa, #c3cfe2);
    --color-fg: #1a1a1a;
    --color-primary: #ffcc00;
    --color-secondary: #ffffff;
    --color-hover: rgba(255, 204, 0, 0.12);
    --color-header-text: #000000;
    --color-button-text: #000000;
    --theme-transition: all 0.15s ease-out;
}

[data-theme="dark"] {
    --color-bg: #0d1117;
    --color-fg: #f5f5f5;
    --color-primary: #ffcc00;
    --color-secondary: #161b22;
    --color-hover: rgba(88, 166, 255, 0.08);
    --color-header-text: #ffffff;
    --color-button-text: #ffffff;
}

/* Dashboard Container */
.dashboard-container {
    margin: 30px auto;
    max-width: 1200px;
    padding: 30px;
    background-color: var(--color-secondary);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding-top: 80px;
}

/* Page Title */
.page-title {
    font-size: 2.8em;
    font-weight: 700;
    margin-bottom: 30px;
    margin-top: 80px;
    color: var(--color-primary);
    text-align: center;
    font-family: 'Cairo', Arial, sans-serif;
}

/* Section Heading */
.section-heading {
    margin: 40px 0 20px 0;
    text-align: center;
}

.section-heading h2 {
    font-size: 2.2em;
    font-weight: 700;
    color: var(--color-primary);
    margin: 0;
    padding: 15px 0;
    border-bottom: 3px solid var(--color-primary);
    display: inline-block;
}

/* Card Styles */
.dashboard-card {
    background: var(--color-secondary);
    border-radius: 18px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
    padding: 22px 20px;
    margin: 12px 0;
    border: 2px solid rgba(255, 204, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary) 50%, var(--color-primary) 100%);
    z-index: 1;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    background: var(--color-hover);
}

.dashboard-card-header {
    background: var(--color-primary);
    color: var(--color-button-text);
    padding: 15px 20px;
    margin: -22px -20px 20px -20px;
    font-weight: 700;
    font-size: 1.1rem;
    border-radius: 18px 18px 0 0;
    position: relative;
    z-index: 2;
}

.dashboard-card-body {
    color: var(--color-fg);
    position: relative;
    z-index: 2;
}

.dashboard-card-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-primary);
    margin: 0 0 10px 0;
}

/* Grid Layout */
.dashboard-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-col-md-4 {
    flex: 1;
    min-width: 300px;
}

.dashboard-col-md-12 {
    width: 100%;
}

/* Filter Form */
.filter-form {
    background: var(--color-secondary);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid rgba(255, 204, 0, 0.1);
    margin-bottom: 30px;
}

.filter-form-header {
    background: var(--color-primary);
    color: var(--color-button-text);
    padding: 15px 20px;
    margin: -25px -25px 20px -25px;
    font-weight: 700;
    font-size: 1.2rem;
    border-radius: 15px 15px 0 0;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-end;
}

.filter-col {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 14px;
    font-weight: bold;
    color: var(--color-fg);
    margin-bottom: 8px;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    font-family: 'Cairo', Arial, sans-serif;
    background: var(--color-secondary);
    color: var(--color-fg);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.filter-input:focus,
.filter-select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 204, 0, 0.2);
    outline: none;
}

.filter-button {
    background-color: var(--color-primary);
    color: var(--color-button-text);
    border: none;
    padding: 12px 25px;
    cursor: pointer;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-right: 10px;
}

.filter-button:hover {
    background-color: rgba(255, 204, 0, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.filter-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-button.secondary {
    background-color: #6c757d;
    color: white;
}

.filter-button.secondary:hover {
    background-color: #5a6268;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    padding: 20px;
}

/* List Styles */
.dashboard-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-list-item {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 204, 0, 0.1);
    font-weight: 500;
    color: var(--color-fg);
    transition: all 0.3s ease;
    background: var(--color-secondary);
}

.dashboard-list-item:hover {
    background: var(--color-hover);
    transform: translateX(5px);
}

.dashboard-list-item:last-child {
    border-bottom: none;
}

/* Dark Mode Support */
[data-theme="dark"] .dashboard-container {
    background-color: var(--color-secondary);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dashboard-card {
    background: var(--color-secondary);
    border-color: rgba(255, 204, 0, 0.2);
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .dashboard-card:hover {
    background: var(--color-hover);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .filter-form {
    background: var(--color-secondary);
    border-color: rgba(255, 204, 0, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .filter-input,
[data-theme="dark"] .filter-select {
    background: var(--color-secondary);
    color: var(--color-fg);
    border-color: #30363d;
}

[data-theme="dark"] .filter-input:focus,
[data-theme="dark"] .filter-select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(255, 204, 0, 0.2);
}

[data-theme="dark"] .dashboard-list-item {
    background: var(--color-secondary);
    border-bottom-color: rgba(255, 204, 0, 0.15);
    color: var(--color-fg);
}

[data-theme="dark"] .dashboard-list-item:hover {
    background: var(--color-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        margin: 10px;
        padding: 15px;
        padding-top: 60px;
    }

    .page-title {
        font-size: 2em;
        margin-top: 60px;
    }

    .section-heading h2 {
        font-size: 1.8em;
    }

    .dashboard-row {
        flex-direction: column;
        gap: 15px;
    }

    .dashboard-col-md-4 {
        min-width: auto;
    }

    .filter-row {
        flex-direction: column;
        gap: 15px;
    }

    .filter-col {
        min-width: auto;
    }

    .chart-container {
        height: 250px;
        padding: 15px;
    }

    .dashboard-card {
        padding: 15px;
        margin: 8px 0;
    }

    .dashboard-card-header {
        margin: -15px -15px 15px -15px;
        padding: 12px 15px;
        font-size: 1rem;
    }

    .dashboard-card-title {
        font-size: 1.4rem;
    }

    .filter-form {
        padding: 20px;
    }

    .filter-form-header {
        margin: -20px -20px 15px -20px;
        padding: 12px 15px;
        font-size: 1.1rem;
    }

    .filter-button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        margin: 5px;
        padding: 10px;
        padding-top: 50px;
    }

    .page-title {
        font-size: 1.6em;
        margin-top: 50px;
    }

    .section-heading h2 {
        font-size: 1.5em;
    }

    .chart-container {
        height: 200px;
        padding: 10px;
    }

    .dashboard-card {
        padding: 12px;
    }

    .dashboard-card-header {
        margin: -12px -12px 12px -12px;
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .dashboard-card-title {
        font-size: 1.2rem;
    }

    .filter-form {
        padding: 15px;
    }

    .filter-form-header {
        margin: -15px -15px 12px -15px;
        padding: 10px 12px;
        font-size: 1rem;
    }

    .filter-input,
    .filter-select {
        padding: 10px 12px;
        font-size: 14px;
    }

    .filter-button {
        padding: 10px 20px;
        font-size: 14px;
    }
}

/* Animation for cards */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-card {
    animation: fadeInUp 0.5s ease-out;
}

/* Hover effects for interactive elements */
.dashboard-card-header:hover {
    background: linear-gradient(45deg, var(--color-primary), rgba(255, 204, 0, 0.8));
}

/* Custom scrollbar for chart containers */
.chart-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.chart-container::-webkit-scrollbar-track {
    background: var(--color-secondary);
    border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 204, 0, 0.8);
}

/* Loading animation for charts */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    color: var(--color-primary);
    font-size: 1.2rem;
}

.chart-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}