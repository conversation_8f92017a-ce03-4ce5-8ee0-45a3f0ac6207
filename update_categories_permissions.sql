-- استعلام تحديث صلاحيات وحدة التصنيفات
-- يضيف الصلاحيات: view, add_category, delete_category, edit_category, add_item_to_category, view_category_items

-- إضافة الصلاحيات الجديدة إذا لم تكن موجودة
INSERT IGNORE INTO permissions (permission_name, permission_name_ar, description) VALUES
('view', 'عرض', 'صلاحية عرض المحتوى'),
('add_category', 'إضافة تصنيف', 'صلاحية إضافة تصنيف جديد'),
('delete_category', 'حذف تصنيف', 'صلاحية حذف التصنيفات'),
('edit_category', 'تعديل تصنيف', 'صلاحية تعديل التصنيفات الموجودة'),
('add_item_to_category', 'إضافة صنف للتصنيف', 'صلاحية إضافة أصناف للتصنيف'),
('view_category_items', 'عرض أصناف التصنيف', 'صلاحية عرض الأصناف الموجودة في التصنيف');

-- التأكد من وجود وحدة categories
INSERT IGNORE INTO modules (module_name, module_name_ar, description, icon_class, is_active, sort_order) VALUES
('categories', 'التصنيفات', 'إدارة تصنيفات المنتجات', 'fas fa-tags', TRUE, 2);

-- ربط الصلاحيات بوحدة categories للدور admin
INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted)
SELECT r.role_id, m.module_id, p.permission_id, TRUE
FROM roles r, modules m, permissions p
WHERE r.role_name = 'admin' 
AND m.module_name = 'categories'
AND p.permission_name IN ('view', 'add_category', 'delete_category', 'edit_category', 'add_item_to_category', 'view_category_items');

-- عرض النتائج
SELECT 'تم تحديث صلاحيات وحدة التصنيفات بنجاح!' as message;

-- عرض الصلاحيات المحدثة
SELECT 'صلاحيات وحدة التصنيفات للمدير:' as info;
SELECT r.role_name_ar, m.module_name_ar, p.permission_name_ar, rp.granted
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.role_id
JOIN modules m ON rp.module_id = m.module_id
JOIN permissions p ON rp.permission_id = p.permission_id
WHERE r.role_name = 'admin' AND m.module_name = 'categories'
ORDER BY p.permission_name;
