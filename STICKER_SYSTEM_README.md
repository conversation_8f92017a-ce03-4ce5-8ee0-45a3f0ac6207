# نظام الاستيكر الموحد - دليل التشغيل
## Unified Sticker System - Operation Guide

## 📋 نظرة عامة

تم تطوير نظام الاستيكر الموحد لتحسين وتوحيد عملية طباعة الاستيكرات في نظام إدارة المخازن. يوفر النظام واجهة موحدة وقابلة للتخصيص مع إعدادات متقدمة.

## 🆕 الملفات الجديدة

### 1. ملفات JavaScript
- **`sticker_system.js`** - النظام الأساسي للاستيكر
- **`sticker_init.js`** - ملف التهيئة وتحميل الإعدادات
- **`multiple_stickers.js`** - محدث لطباعة عدة ملصقات

### 2. مل<PERSON><PERSON><PERSON> CSS
- **`sticker_styles.css`** - أن<PERSON><PERSON><PERSON> موحدة ومتجاوبة للاستيكر

### 3. ملفات PHP
- **`sticker_config.php`** - إدارة إعدادات الاستيكر
- **`sticker_settings.php`** - واجهة إعدادات الاستيكر
- **`add_sticker_permissions.php`** - إضافة صلاحيات النظام

## 🔧 الملفات المحدثة

### 1. ملفات الأصناف
- **`items.php`** - محدث للنظام الموحد
- **`store_items.php`** - محدث للنظام الموحد
- **`sidebar.php`** - إضافة رابط إعدادات الاستيكر

### 2. ملفات JavaScript المحدثة
- **`sticker.js`** - تحسينات في معالجة الأخطاء

## 🚀 خطوات التشغيل

### 1. رفع الملفات
```bash
# رفع جميع الملفات الجديدة إلى مجلد المشروع
- sticker_system.js
- sticker_init.js
- sticker_styles.css
- sticker_config.php
- sticker_settings.php
- add_sticker_permissions.php
```

### 2. إضافة الصلاحيات
1. افتح المتصفح واذهب إلى: `your-domain.com/add_sticker_permissions.php`
2. اضغط على "إضافة الصلاحيات"
3. انتظر رسالة التأكيد

### 3. إعداد قاعدة البيانات
سيتم إنشاء جدول `sticker_settings` تلقائياً عند أول استخدام.

### 4. الوصول للإعدادات
- اذهب إلى القائمة الجانبية
- اضغط على "إعدادات الاستيكر"
- قم بتخصيص الإعدادات حسب احتياجاتك

## ⚙️ الإعدادات المتاحة

### 1. الأبعاد
- **العرض**: حجم عرض الاستيكر (مثال: 60mm)
- **الارتفاع**: حجم ارتفاع الاستيكر (مثال: 35mm)

### 2. المظهر
- **صورة الخلفية**: مسار صورة خلفية الاستيكر
- **اسم العلامة التجارية**: النص المعروض في أعلى الاستيكر
- **نوع الخط**: خط النصوص في الاستيكر

### 3. الباركود
- **تنسيق الباركود**: CODE128, CODE39, EAN13
- **حجم رمز QR**: حجم رمز الاستجابة السريعة

### 4. إعدادات أخرى
- **رمز العملة**: العملة المعروضة مع السعر
- **جودة الطباعة**: مستوى جودة الطباعة

## 🎯 المميزات الجديدة

### 1. واجهة موحدة
- تصميم متسق عبر جميع الصفحات
- أنماط CSS موحدة ومتجاوبة
- دعم الوضع المظلم

### 2. إعدادات قابلة للتخصيص
- تخصيص أبعاد الاستيكر
- تغيير الخطوط والألوان
- إعدادات الباركود المتقدمة

### 3. معالجة أفضل للأخطاء
- رسائل خطأ واضحة ومفيدة
- التحقق من صحة البيانات
- معالجة حالات الفشل

### 4. أدوات إدارية
- تصدير واستيراد الإعدادات
- إعادة تعيين للقيم الافتراضية
- معاينة مباشرة للتغييرات

## 🔒 الصلاحيات

### صلاحيات إعدادات الاستيكر
- **عرض**: رؤية صفحة الإعدادات
- **تعديل**: تغيير الإعدادات
- **تصدير**: تصدير ملف الإعدادات
- **استيراد**: استيراد إعدادات من ملف
- **إعادة تعيين**: العودة للإعدادات الافتراضية

### صلاحيات طباعة الاستيكر
- **طباعة الاستيكر**: طباعة ملصقات الأصناف

## 🛠️ استكشاف الأخطاء

### 1. لا تظهر إعدادات الاستيكر في القائمة
- تأكد من تشغيل `add_sticker_permissions.php`
- تحقق من صلاحيات المستخدم
- تأكد من تحديث ملف `sidebar.php`

### 2. لا تعمل طباعة الاستيكر
- تحقق من تحميل ملف `sticker_system.js`
- تأكد من وجود مكتبات JsBarcode و QRCode
- تحقق من إعدادات المتصفح للنوافذ المنبثقة

### 3. لا تحفظ الإعدادات
- تحقق من صلاحيات الكتابة في قاعدة البيانات
- تأكد من وجود جدول `sticker_settings`
- تحقق من صحة البيانات المدخلة

### 4. مشاكل في التصميم
- تأكد من تحميل ملف `sticker_styles.css`
- تحقق من تضارب أنماط CSS
- تأكد من دعم المتصفح لـ CSS Grid

## 📱 التوافق

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### الأجهزة
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية

## 🔄 التحديثات المستقبلية

### المخطط لها
- دعم قوالب متعددة للاستيكر
- إعدادات متقدمة للألوان
- دعم طباعة مجمعة محسنة
- تكامل مع أنظمة طباعة خارجية

### تحت الدراسة
- دعم أحجام ورق مختلفة
- قوالب مخصصة للعملاء
- تصدير PDF للملصقات
- API للتكامل الخارجي

## 📞 الدعم الفني

في حالة مواجهة أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع ملفات السجل (logs)
3. تأكد من تحديث جميع الملفات
4. اتصل بفريق الدعم الفني

## 📝 ملاحظات مهمة

- **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل التحديث
- **الاختبار**: اختبر النظام في بيئة تطوير أولاً
- **الصلاحيات**: تأكد من إعداد الصلاحيات بشكل صحيح
- **الأداء**: راقب أداء النظام بعد التحديث

---

**تاريخ آخر تحديث**: ديسمبر 2024  
**الإصدار**: 2.0  
**المطور**: فريق تطوير نظام إدارة المخازن
