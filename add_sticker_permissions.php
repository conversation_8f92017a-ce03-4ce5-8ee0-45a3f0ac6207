<?php
/**
 * إضافة صلاحيات نظام الاستيكر
 * Add Sticker System Permissions
 */

require_once 'db_connection.php';
require_once 'security.php';

// التحقق من أن المستخدم مدير
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

try {
    // بدء المعاملة
    $conn->begin_transaction();
    
    // إضافة وحدة إعدادات الاستيكر
    $module_stmt = $conn->prepare("INSERT IGNORE INTO modules (module_name, module_name_ar, is_active, icon_class) VALUES (?, ?, TRUE, ?)");
    $module_name = 'sticker_settings';
    $module_name_ar = 'إعدادات الاستيكر';
    $icon_class = 'fas fa-tags';
    $module_stmt->bind_param("sss", $module_name, $module_name_ar, $icon_class);
    $module_stmt->execute();
    $module_stmt->close();
    
    // الحصول على معرف الوحدة
    $module_id_stmt = $conn->prepare("SELECT module_id FROM modules WHERE module_name = ?");
    $module_id_stmt->bind_param("s", $module_name);
    $module_id_stmt->execute();
    $module_result = $module_id_stmt->get_result();
    $module_row = $module_result->fetch_assoc();
    $module_id = $module_row['module_id'];
    $module_id_stmt->close();
    
    // إضافة الصلاحيات للوحدة
    $permissions = [
        ['view', 'عرض إعدادات الاستيكر'],
        ['edit', 'تعديل إعدادات الاستيكر'],
        ['export', 'تصدير إعدادات الاستيكر'],
        ['import', 'استيراد إعدادات الاستيكر'],
        ['reset', 'إعادة تعيين إعدادات الاستيكر']
    ];
    
    $permission_stmt = $conn->prepare("INSERT IGNORE INTO permissions (permission_name, permission_name_ar) VALUES (?, ?)");
    
    foreach ($permissions as $permission) {
    $permission_stmt->bind_param("ss", $permission[0], $permission[1]);
    $permission_stmt->execute();
    }
    $permission_stmt->close();
    
    // إضافة صلاحيات طباعة الاستيكر للوحدة الموجودة
    $items_module_stmt = $conn->prepare("SELECT module_id FROM modules WHERE module_name = 'items'");
    $items_module_stmt->execute();
    $items_result = $items_module_stmt->get_result();
    
    if ($items_row = $items_result->fetch_assoc()) {
        $items_module_id = $items_row['module_id'];
        
        // إضافة صلاحية طباعة الاستيكر
        $sticker_permission_stmt = $conn->prepare("INSERT IGNORE INTO permissions (permission_name, permission_name_ar) VALUES (?, ?)");
        $print_sticker_permission = 'print_sticker';
        $print_sticker_description = 'طباعة الاستيكر';
        $sticker_permission_stmt->bind_param("ss", $print_sticker_permission, $print_sticker_description);
        $sticker_permission_stmt->execute();
        $sticker_permission_stmt->close();
    }
    $items_module_stmt->close();
    
    // منح الصلاحيات للمدير
    $admin_role_stmt = $conn->prepare("SELECT role_id FROM roles WHERE role_name = 'admin'");
    $admin_role_stmt->execute();
    $admin_result = $admin_role_stmt->get_result();
    
    if ($admin_row = $admin_result->fetch_assoc()) {
        $admin_role_id = $admin_row['role_id'];
        
        $role_permission_stmt = $conn->prepare("INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (?, ?, ?, 1)");
        
        foreach ($permissions as $permission) {
        $get_id_stmt = $conn->prepare("SELECT permission_id FROM permissions WHERE permission_name = ?");
        $get_id_stmt->bind_param("s", $permission[0]);
        $get_id_stmt->execute();
        $get_result = $get_id_stmt->get_result();
        if ($row = $get_result->fetch_assoc()) {
        $perm_id = $row['permission_id'];
        $role_permission_stmt->bind_param("iii", $admin_role_id, $module_id, $perm_id);
        $role_permission_stmt->execute();
        }
        $get_id_stmt->close();
        }
        $role_permission_stmt->close();
        
        // منح صلاحية طباعة الاستيكر للمدير
        if (isset($items_module_id)) {
            $print_permission_stmt = $conn->prepare("SELECT permission_id FROM permissions WHERE permission_name = 'print_sticker'");
            $print_permission_stmt->execute();
            $print_result = $print_permission_stmt->get_result();
            
            if ($print_row = $print_result->fetch_assoc()) {
                $print_permission_id = $print_row['permission_id'];
                $admin_print_stmt = $conn->prepare("INSERT IGNORE INTO role_permissions (role_id, module_id, permission_id, granted) VALUES (?, ?, ?, 1)");
                $admin_print_stmt->bind_param("iii", $admin_role_id, $items_module_id, $print_permission_id);
                $admin_print_stmt->execute();
                $admin_print_stmt->close();
            }
            $print_permission_stmt->close();
        }
    }
    $admin_role_stmt->close();
    
    // تأكيد المعاملة
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة صلاحيات نظام الاستيكر بنجاح'
    ]);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollback();
    
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في إضافة الصلاحيات: ' . $e->getMessage()
    ]);
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة صلاحيات الاستيكر</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container" style="max-width: 600px; margin: 50px auto; padding: 20px;">
        <h1>إضافة صلاحيات نظام الاستيكر</h1>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>الصلاحيات التي سيتم إضافتها:</h3>
            <ul>
                <li><strong>وحدة إعدادات الاستيكر:</strong>
                    <ul>
                        <li>عرض إعدادات الاستيكر</li>
                        <li>تعديل إعدادات الاستيكر</li>
                        <li>تصدير إعدادات الاستيكر</li>
                        <li>استيراد إعدادات الاستيكر</li>
                        <li>إعادة تعيين إعدادات الاستيكر</li>
                    </ul>
                </li>
                <li><strong>وحدة الأصناف:</strong>
                    <ul>
                        <li>طباعة الاستيكر</li>
                    </ul>
                </li>
            </ul>
        </div>
        
        <button id="addPermissions" class="btn btn-primary" style="width: 100%; padding: 15px; font-size: 16px;">
            إضافة الصلاحيات
        </button>
        
        <div id="result" style="margin-top: 20px;"></div>
    </div>
    
    <script>
        document.getElementById('addPermissions').addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'جاري الإضافة...';
            
            fetch('add_sticker_permissions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=add_permissions'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم بنجاح',
                        text: data.message,
                        confirmButtonText: 'حسناً'
                    }).then(() => {
                        window.location.href = 'sticker_settings.php';
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.message,
                        confirmButtonText: 'حسناً'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ في الاتصال',
                    confirmButtonText: 'حسناً'
                });
            })
            .finally(() => {
                this.disabled = false;
                this.textContent = 'إضافة الصلاحيات';
            });
        });
    </script>
</body>
</html>
