<?php
include 'db_connection.php';
require_once 'security.php';
include 'encryption_functions.php';

$key = getenv('ENCRYPTION_KEY');

if (isset($_GET['category_id'])) {
    $encrypted_category_id = urldecode($_GET['category_id']);
    $category_id = decrypt($encrypted_category_id, $key);

    if ($category_id === false) {
        echo "<p>Invalid category ID.</p>";
        exit();
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_item_id'])) {
        $delete_item_id = $_POST['delete_item_id'];

        // Delete images from the folder
        $img_stmt = $conn->prepare("SELECT img_path FROM itemimages WHERE item_id = ?");
        $img_stmt->bind_param("i", $delete_item_id);
        $img_stmt->execute();
        $img_result = $img_stmt->get_result();
        while ($img_row = $img_result->fetch_assoc()) {
            if (file_exists($img_row['img_path'])) {
                unlink($img_row['img_path']);
            }
        }
        $img_stmt->close();

        // Delete related data from other tables
        $related_tables = [];
        $result = $conn->query("SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE REFERENCED_TABLE_NAME = 'items' AND REFERENCED_COLUMN_NAME = 'item_id'");
        while ($row = $result->fetch_assoc()) {
            $related_tables[] = $row;
        }

        foreach ($related_tables as $table) {
            $stmt = $conn->prepare("DELETE FROM {$table['TABLE_NAME']} WHERE {$table['COLUMN_NAME']} = ?");
            $stmt->bind_param("i", $delete_item_id);
            $stmt->execute();
            $stmt->close();
        }

        $stmt = $conn->prepare("DELETE FROM items WHERE item_id = ?");
        $stmt->bind_param("i", $delete_item_id);
        $stmt->execute();
        $stmt->close();
        
        exit();
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['toggle_item_id'])) {
        $toggle_item_id = $_POST['toggle_item_id'];
        $current_status = $_POST['current_status'];
        $new_status = $current_status ? 0 : 1;
        $stmt = $conn->prepare("UPDATE items SET item_use = ? WHERE item_id = ?");
        $stmt->bind_param("ii", $new_status, $toggle_item_id);
        $stmt->execute();
        $stmt->close();
        
        exit();
    }

    $sql = "SELECT * FROM items WHERE category_id = ? ORDER BY item_id DESC"; // ترتيب الأصناف حسب الأحدث
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
} else {
    echo "<p>Category ID is missing.</p>";
    exit();
}

$encrypted_store_id = isset($_GET['store_id']) ? urldecode($_GET['store_id']) : (isset($_SESSION['store_id']) ? $_SESSION['store_id'] : null);
$store_name = '';

if ($encrypted_store_id) {
    $store_id = decrypt($encrypted_store_id, $key);
    $stmt = $conn->prepare("SELECT name FROM stores WHERE store_id = ?");
    $stmt->bind_param("i", $store_id);
    $stmt->execute();
    $stmt->bind_result($store_name);
    $stmt->fetch();
    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <link href="uploads\img\logo.png" rel="icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الأصناف</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="sticker_styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <!-- النظام الموحد للاستيكر -->
    <script src="sticker_system.js"></script>
    <script src="multiple_stickers.js"></script>
    <script src="sticker_init.js"></script>
    <style>
        #sidebar ul li.branch {
    padding: 10px 15px;
    background-color: #2c3e50; /* لون خلفية مميز */
    color: #ecf0f1;
    border-radius: 5px;
    margin: 10px 0;
  }
  .branch-container {
    display: flex;
    align-items: center;
  }
  .branch-icon {
    margin-right: 8px;
  }
  .branch-text {
    font-size: 1.1rem;
    font-weight: bold;
  }
  .editable {
    cursor: pointer;
    
}

.editable:hover {
    color: #007bff; /* لون أزرق عند المرور */
}

.editable-input {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
    border: 1px solid #aaa;
    border-radius: 6px;
    font-size: 1em;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.editable-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    outline: none;
}

/* Restore natural font size for product name column */
.editable[data-field='name'] {
    font-size: inherit;
}
.fixed-navbar {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #2b3a53;
    padding: 10px 0;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
    z-index: 1000;
}

.fixed-navbar .btn-danger {
    margin: 0 auto;
}
/* Override fixed navbar (printing bar) z-index */
.fixed-navbar {
    z-index: 500 !important;
}
/* ...existing code... */
.modal {
    z-index: 1500 !important;
}
/* ...existing code... */
    </style>
</head>
<body>
<?php include 'sidebar.php'; ?>


<div class="container">
    <h2>قائمة الأصناف</h2>


    <input type="text" id="searchField" class="search-bar" placeholder="ابحث عن صنف...">

    <?php if (hasPermission('items', 'add')): ?>
    <button class="add-btn" onclick="openAddItemModal()">إضافة صنف جديد</button>
    <?php endif; ?>

    <form method="POST" action="delete_selected_items.php" id="deleteForm">
        <input type="hidden" name="category_id" value="<?php echo htmlspecialchars($encrypted_category_id); ?>">
        <div class="table-responsive">

        <table>
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAll"></th>
                    <!-- New column for the printer icon -->
                    <th>ملصق</th>
                    <th>اسم الصنف</th>
                    <th>التكلفة</th>
                    <th>سعر البيع الأول</th>
                    <th>سعر البيع الثاني</th>
                    <th>سعر البيع الثالث</th>
                    <th>سعر البيع الرابع</th>
                    <th>الوصف</th>
                    <th>الباركود</th>
                    <!-- Removed old "الملصق" column -->
                    <th>الكمية</th>
                    <th>حالة المنتج</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody id="itemsTable">
                <?php
                if ($result && $result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $toggle_class = $row['item_use'] ? '' : 'inactive';
                        $toggle_text = $row['item_use'] ? 'نشط' : 'متوقف';
                        $encrypted_item_id = encrypt($row['item_id'], $key);
                        echo "<tr id='item-{$row['item_id']}' onclick='toggleCheckbox(this)'>
                                <td class='checkbox-cell'><input type='checkbox' name='selected_items[]' value='" . htmlspecialchars($row['item_id']) . "'></td>";
                        if (hasPermission('items', 'print_sticker')) {
                            echo "<!-- Changed: call new choosePriceAndPrint() -->
                                <td>
                                    <img src='icons/printer.png' style='width:24px; height:24px; cursor:pointer;' 
                                    onclick='event.stopPropagation(); choosePriceAndPrint(\"" . addslashes(htmlspecialchars($row['name'])) . "\", \"" . htmlspecialchars($row['barcode']) . "\", \"" . htmlspecialchars($row['price1']) . "\", \"" . htmlspecialchars($row['price2']) . "\", \"" . htmlspecialchars($row['price3']) . "\", \"" . htmlspecialchars($row['price4']) . "\")' alt='طباعة' />
                                </td>";
                        } else {
                            echo "<td></td>";
                        }
                        echo "<td class='editable' data-field='name' data-id='{$row['item_id']}'>" . htmlspecialchars($row['name']) . "</td>
                                <td class='editable' data-field='cost' data-id='{$row['item_id']}'>" . htmlspecialchars($row['cost']) . "</td>
                                <td class='editable' data-field='price1' data-id='{$row['item_id']}'>" . htmlspecialchars($row['price1']) . "</td>
                                <td class='editable' data-field='price2' data-id='{$row['item_id']}'>" . htmlspecialchars($row['price2']) . "</td>
                                <td class='editable' data-field='price3' data-id='{$row['item_id']}'>" . htmlspecialchars($row['price3']) . "</td>
                                <td class='editable' data-field='price4' data-id='{$row['item_id']}'>" . htmlspecialchars($row['price4']) . "</td>
                                <td>
                                    <i class='fas fa-eye' style='color:#4A9AC4; cursor:pointer; font-size:18px;' onclick='event.stopPropagation(); showDescription(\"" . htmlspecialchars($row['name']) . "\", \"" . htmlspecialchars($row['description']) . "\")'></i>
                                </td>
                                <td class='editable' data-field='barcode' data-id='{$row['item_id']}'>" . htmlspecialchars($row['barcode']) . "</td>
                                <!-- Removed old sticker button cell -->
                                <td class='editable' data-field='quantity' data-id='{$row['item_id']}'>" . htmlspecialchars($row['quantity']) . "</td>
                                <td onclick='event.stopPropagation()'>";
                        if (hasPermission('items', 'edit')) {
                            echo "<button class='toggle-btn $toggle_class' type='button' onclick='toggleItemStatus(" . htmlspecialchars($row['item_id']) . ", " . htmlspecialchars($row['item_use']) . ")'>$toggle_text</button>";
                        } else {
                            echo $toggle_text;
                        }
                        echo "</td>
                                <td onclick='event.stopPropagation()' style='text-align:center; padding:12px;'>";
                        if (hasPermission('items', 'edit')) {
                            echo "<span class='action-icon edit-icon' onclick='editItem(\"" . htmlspecialchars($encrypted_item_id) . "\")'><i class='fas fa-edit'></i></span>";
                        }
                        if (hasPermission('items', 'delete')) {
                            echo "<span class='action-icon delete-icon' onclick='deleteItem(" . htmlspecialchars($row['item_id']) . ")'><i class='fas fa-trash-alt'></i></span>";
                        }
                        echo "</td>
                              </tr>";
                    }
                } else {
                    echo "<tr><td colspan='13'>لا توجد أصناف حالياً</td></tr>";
                }
                ?>
            </tbody>
        </table>
        </div>
        <?php if (hasPermission('items', 'delete')): ?>
        <button type="submit" class="action-btn">حذف الأصناف المحددة</button>
        <?php endif; ?>
    </form>
     <div class="items-action-bar">
         <?php if (hasPermission('items', 'print_sticker')): ?>
         <button type="button" class="action-bar-btn print-btn" onclick="printSelectedStickers()">
             <i class="fas fa-print"></i>
             طباعة الأصناف المحددة
         </button>
         <?php endif; ?>
         <?php if (hasPermission('items', 'edit')): ?>
         <button type="button" class="action-bar-btn activate-btn" onclick="toggleSelectedItemsStatus(1)">
             <i class="fas fa-toggle-on"></i>
             تنشيط الأصناف المحددة
         </button>
         <button type="button" class="action-bar-btn deactivate-btn" onclick="toggleSelectedItemsStatus(0)">
             <i class="fas fa-toggle-off"></i>
             ايقاف الأصناف المحددة
         </button>
         <?php endif; ?>
     </div>
<script>
        // Add click animation to action bar buttons
        document.addEventListener('DOMContentLoaded', function() {
            const actionBarButtons = document.querySelectorAll('.action-bar-btn');
            actionBarButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.add('clicked');
                    setTimeout(() => {
                        this.classList.remove('clicked');
                    }, 300);
                });
            });
        });

        // ...existing code...
        function printSelectedStickers() {
            const selectedCheckboxes = document.querySelectorAll('input[name="selected_items[]"]:checked');
            if (selectedCheckboxes.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'تنبيه',
                    text: 'لم يتم تحديد أصناف للطباعة',
                    confirmButtonText: 'حسناً'
                });
                return;
            }
            // Price selection is now handled here and forwarded to printMultipleStickers
            Swal.fire({
                title: 'اختر السعر للملصقات',
                input: 'select',
                inputOptions: {
                    '0': 'بدون سعر',
                    '1': 'السعر الأول',
                    '2': 'السعر الثاني',
                    '3': 'السعر الثالث',
                    '4': 'السعر الرابع'
                },
                inputPlaceholder: 'اختر السعر',
                showCancelButton: true,
                allowOutsideClick: false
            }).then((result) => {
                let stickersArray = [];
                selectedCheckboxes.forEach(checkbox => {
                    const row = checkbox.closest("tr");
                    const itemName = row.cells[2].innerHTML.trim();
                    const barcode = row.cells[9].textContent.trim();
                    let price = "";
                    if (result.isConfirmed) {
                        switch (result.value) {
                            case '0':
                                price = ""; // No price option
                                break;
                            case '1':
                                price = row.cells[4].textContent.trim();
                                break;
                            case '2':
                                price = row.cells[5].textContent.trim();
                                break;
                            case '3':
                                price = row.cells[6].textContent.trim();
                                break;
                            case '4':
                                price = row.cells[7].textContent.trim();
                                break;
                            default:
                                price = row.cells[4].textContent.trim();
                        }
                    } else {
                        price = row.cells[4].textContent.trim();
                    }
                    stickersArray.push({ itemName, barcode, price });
                });
                printMultipleStickers(stickersArray);
            });
        }
        // ...existing code...
    function toggleSelectedItemsStatus(newStatus) {
        const selectedCheckboxes = document.querySelectorAll('input[name="selected_items[]"]:checked');
        if (selectedCheckboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'لم يتم تحديد أصناف لتغيير حالتها',
                confirmButtonText: 'حسناً'
            });
            return;
        }
        let ids = [];
        selectedCheckboxes.forEach(checkbox => {
            ids.push(checkbox.value);
        });
        fetch('toggle_selected_items.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ item_ids: ids, new_status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if(data.success) {
                // تحديث عمود حالة المنتج لكل صف
                selectedCheckboxes.forEach(checkbox => {
                    const row = checkbox.closest("tr");
                    const toggleBtn = row.cells[11].querySelector('.toggle-btn');
                    if(toggleBtn) {
                        toggleBtn.textContent = newStatus ? 'نشط' : 'متوقف';
                        if(newStatus) {
                            toggleBtn.classList.remove('inactive');
                        } else {
                            toggleBtn.classList.add('inactive');
                        }
                    }
                    // إلغاء تحديد الشيكبوكس بعد نجاح التحديث
                    checkbox.checked = false;
                });
            } else {
                alert("فشل تحديث حالة الأصناف: " + data.message);
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("حدث خطأ أثناء تحديث الحالة.");
        });
    }
</script>
<style>
/* New style for buttons used in bulk status update */
.selected-item-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}
</style>
<div id="editItemModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>تعديل تفاصيل الصنف</h2>
        <form method="POST" action="edit_item.php" enctype="multipart/form-data">
            <!-- الحقول المخفية -->
            <input type="hidden" name="edit_item_id" id="edit_item_id">
            <input type="hidden" name="existing_img_path" id="existing_img_path">
            <input type="hidden" name="deleted_images" id="deleted_images">
            <input type="hidden" name="category_id" value="<?php echo isset($encrypted_category_id) ? htmlspecialchars($encrypted_category_id) : ''; ?>">

            <!-- حقل اسم الصنف -->
            <label for="edit_item_name">اسم الصنف:</label>
            <input type="text" name="item_name" id="edit_item_name" class="input-field" placeholder="اسم الصنف" required>

            <!-- حقل تكلفة المنتج -->
            <label for="edit_cost">تكلفة المنتج:</label>
            <input type="number" step="0.01" name="cost" id="edit_cost" class="input-field" placeholder="تكلفة المنتج" required>

            <!-- حقل سعر البيع الأول -->
            <label for="edit_price1">سعر البيع الأول:</label>
            <input type="number" step="0.01" name="price1" id="edit_price1" class="input-field" placeholder="سعر البيع الأول" required>

            <!-- حقل سعر البيع الثاني -->
            <label for="edit_price2">سعر البيع الثاني:</label>
            <input type="number" step="0.01" name="price2" id="edit_price2" class="input-field" placeholder="سعر البيع الثاني" required>

            <!-- حقل سعر البيع الثالث -->
            <label for="edit_price3">سعر البيع الثالث:</label>
            <input type="number" step="0.01" name="price3" id="edit_price3" class="input-field" placeholder="سعر البيع الثالث" required>

            <!-- حقل سعر البيع الرابع -->
            <label for="edit_price4">سعر البيع الرابع:</label>
            <input type="number" step="0.01" name="price4" id="edit_price4" class="input-field" placeholder="سعر البيع الرابع" required>

            <!-- حقل وصف المنتج -->
            <label for="edit_description">وصف المنتج:</label>
            <textarea name="description" id="edit_description" class="input-field" placeholder="وصف المنتج" ></textarea>

            <!-- حقل كمية المنتج -->
            <label for="edit_quantity">الكمية:</label>
            <input type="number" name="quantity" id="edit_quantity" class="input-field" placeholder="الكمية" required>

            <!-- معاينة الصور وحقل رفع الصور -->
            <div class="image-preview" id="image_preview"></div>
            <label for="img_path">صور المنتج:</label>
            <div>
                <input type="file" name="img_path[]" id="img_path" class="input-field" multiple>
            </div>

            <!-- حقل باركود المنتج -->
            <label for="edit_barcode">باركود المنتج:</label>
            <input type="text" name="barcode" id="edit_barcode" class="input-field" placeholder="باركود المنتج" required>

            <!-- خيار استخدام المنتج -->
            <label for="edit_item_use">
                <input type="checkbox" name="item_use" id="edit_item_use">
                استخدام المنتج
            </label>

            <!-- خيار إضافة خصم -->
            <label for="add_discount_checkbox">
                <input type="checkbox" id="add_discount_checkbox" onclick="toggleDiscountInput()">
                إضافة خصم
            </label>

            <!-- حقل الخصم يظهر عند تفعيل خيار الخصم -->
            <div id="discount_input_container" style="display: none;">
                <label for="edit_discount">قيمة الخصم:</label>
                <input type="number" step="0.01" name="discount" id="edit_discount" class="input-field" placeholder="قيمة الخصم">
            </div>

            <button type="submit" class="add-btn">تعديل الصنف</button>
        </form>
    </div>
</div>


<div id="addItemModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeAddItemModal()">&times;</span>
        <h2>إضافة صنف جديد</h2>
        <form method="POST" action="add_item.php" enctype="multipart/form-data">
            <input type="hidden" name="add_item" value="1">
            <input type="hidden" name="category_id" value="<?php echo isset($category_id) ? htmlspecialchars($category_id) : ''; ?>">
            <label for="item_name">اسم الصنف</label>
            <input type="text" name="item_name" class="input-field" placeholder="اسم الصنف" required>
            <label for="cost">تكلفة المنتج</label>
            <input type="number" step="0.01" name="cost" class="input-field" placeholder="تكلفة المنتج" required>
            <label for="price1">سعر البيع الأول</label>
            <input type="number" step="0.01" name="price1" class="input-field" placeholder="سعر البيع الأول" required>
            <label for="price2">سعر البيع الثاني</label>
            <input type="number" step="0.01" name="price2" class="input-field" placeholder="سعر البيع الثاني" required>
            <label for="price3">سعر البيع الثالث</label>
            <input type="number" step="0.01" name="price3" class="input-field" placeholder="سعر البيع الثالث" required>
            <label for="price4">سعر البيع الرابع</label>
            <input type="number" step="0.01" name="price4" class="input-field" placeholder="سعر البيع الرابع" required>
            <label for="quantity">الكمية</label>
            <input type="number" name="quantity" class="input-field" placeholder="الكمية" required>
            <label for="description">وصف المنتج</label>
            <textarea name="description" class="input-field" placeholder="وصف المنتج" required></textarea>
            <label for="img_path">صور المنتج</label>
            <input type="file" name="img_path[]" class="input-field" multiple>
            <label for="barcode">باركود المنتج</label>
            <input type="text" id="barcode" name="barcode" class="input-field" placeholder="باركود المنتج" disabled>
            <label>
                <input type="checkbox" id="auto_generate_barcode" name="auto_generate_barcode" checked> توليد باركود تلقائي
            </label>
            <label>
                <input type="checkbox" name="item_use"> استخدام المنتج
            </label>
            <button type="submit" class="add-btn">إضافة الصنف</button>
        </form>
    </div>
</div>

<div id="descriptionModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeDescriptionModal()">&times;</span>
        <h2 id="itemName"></h2>
        <p id="descriptionText"></p>
    </div>
</div>

<div id="stickerModal" class="sticker-modal">
    <div class="sticker-modal-content">
        <span class="sticker-modal-close" onclick="closeStickerModal()">&times;</span>
        <h3 class="sticker-modal-title" id="stickerItemName">معاينة الاستيكر</h3>

        <div class="sticker-container">
            <div class="sticker-preview" id="stickerPreview">
                <div id="stickerItemLabel" class="sticker-content"></div>
                <div id="actualBarcode" style="display: none;"></div>
            </div>

            <div class="sticker-controls">
                <button class="sticker-print-btn" onclick="printSticker()">
                    <i class="fas fa-print"></i> طباعة الاستيكر
                </button>
            </div>

            <div class="sticker-info">
                <small>يمكنك معاينة الاستيكر قبل الطباعة والتأكد من صحة البيانات</small>
            </div>
        </div>
    </div>
</div>

<div class="popup-message" id="popupMessage"></div>

<script>
    var editItemModal = document.getElementById("editItemModal");
    var editItemSpan = document.getElementsByClassName("close")[0];
    var deletedImages = [];

    editItemSpan.onclick = function() {
        editItemModal.style.display = "none";
    }

    window.onclick = function(event) {
        if (event.target == editItemModal) {
            editItemModal.style.display = "none";
        }
    }

    function editItem(encryptedItemId) {
        // Fetch data for the selected item using AJAX
        fetch(`get_item.php?item_id=${encodeURIComponent(encryptedItemId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('edit_item_id').value = encryptedItemId;
                    document.getElementById('edit_item_name').value = data.item.name;
                    document.getElementById('edit_cost').value = data.item.cost;
                    document.getElementById('edit_price1').value = data.item.price1;
                    document.getElementById('edit_price2').value = data.item.price2;
                    document.getElementById('edit_price3').value = data.item.price3;
                    document.getElementById('edit_price4').value = data.item.price4;
                    document.getElementById('edit_description').value = data.item.description;
                    document.getElementById('edit_barcode').value = data.item.barcode;
                    document.getElementById('existing_img_path').value = data.item.img_path;
                    document.getElementById('edit_item_use').checked = data.item.item_use;
                    document.getElementById('edit_quantity').value = data.item.quantity;

                    // Display discount if available
                    if (data.item.discount) {
                        document.getElementById('add_discount_checkbox').checked = true;
                        document.getElementById('discount_input_container').style.display = 'block';
                        document.getElementById('edit_discount').value = data.item.discount;
                    } else {
                        document.getElementById('add_discount_checkbox').checked = false;
                        document.getElementById('discount_input_container').style.display = 'none';
                        document.getElementById('edit_discount').value = '';
                    }

                    // Display images
                    const imagePreview = document.getElementById('image_preview');
                    imagePreview.innerHTML = '';
                    const imgPaths = data.item.images.map(image => image.img_path);
                    imgPaths.forEach(path => {
                        const imgContainer = document.createElement('div');
                        imgContainer.style.position = 'relative';
                        const img = document.createElement('img');
                        img.src = path;
                        const deleteBtn = document.createElement('button');
                        deleteBtn.classList.add('delete-btn');
                        deleteBtn.innerHTML = '&times;';
                        deleteBtn.onclick = function() {
                            imgContainer.remove();
                            deletedImages.push(path);
                            document.getElementById('deleted_images').value = JSON.stringify(deletedImages);
                        };
                        imgContainer.appendChild(img);
                        imgContainer.appendChild(deleteBtn);
                        imagePreview.appendChild(imgContainer);
                    });

                    // Display modal
                    editItemModal.style.display = "block";
                } else {
                    alert("Failed to fetch item details.");
                }
            })
            .catch(error => {
                console.error("Error fetching item:", error);
            });
    }

    function deleteItem(itemId) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('delete_item_id', itemId);

                fetch('delete_item.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم حذف الصنف بنجاح',
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            document.getElementById(`item-${itemId}`).remove();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'لا يمكن حذف الصنف',
                            text: data.message
                        });
                    }
                })
                .catch(error => console.error('Error:', error));
            }
        });
    }

    function toggleItemStatus(itemId, currentStatus) {
        const formData = new FormData();
        formData.append('toggle_item_id', itemId);
        formData.append('current_status', currentStatus);

        fetch('toggle_item_status.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const button = document.querySelector(`button[onclick='toggleItemStatus(${itemId}, ${currentStatus})']`);
                button.textContent = data.new_status ? 'نشط' : 'متوقف';
                button.classList.toggle('inactive', !data.new_status);
                button.setAttribute('onclick', `toggleItemStatus(${itemId}, ${data.new_status})`);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في تحديث حالة الصنف',
                    text: 'حدث خطأ أثناء تحديث حالة الصنف. حاول مرة أخرى.'
                });
            }
        })
        .catch(error => {
            console.error("Error toggling item status:", error);
        });
    }

    document.getElementById('selectAll').onclick = function() {
        var checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        for (var checkbox of checkboxes) {
            checkbox.checked = this.checked;
        }
    }

    document.addEventListener("DOMContentLoaded", function() {
        document.getElementById('searchField').onkeyup = function() {
            var filter = this.value.toLowerCase();
            var rows = document.querySelectorAll('#itemsTable tr');
            rows.forEach(row => {
                // Updated indexes: 2 for name, 8 for description, and 9 for barcode
                var name = row.cells[2].textContent.toLowerCase();
                var description = row.cells[8].textContent.toLowerCase();
                var barcode = row.cells[9].textContent.toLowerCase();
                row.style.display = (name.includes(filter) || description.includes(filter) || barcode.includes(filter)) ? '' : 'none';
            });
        };
        
        // New: Adjust font size of product name dynamically based on text length
        document.querySelectorAll('.editable[data-field="name"]').forEach(cell => {
            let len = cell.textContent.trim().length;
            // Example formula: decrease size as length increases (min size: 10px, base: 16px)
            let size = Math.max(16 - Math.floor((len - 10) / 5), 10);
            cell.style.fontSize = size + "px";
        });
    });

    function toggleCheckbox(row) {
        var checkbox = row.querySelector('input[type="checkbox"]');
        checkbox.checked = !checkbox.checked;
    }

    function showPopupMessage(message, type) {
        const popupMessage = document.getElementById('popupMessage');
        popupMessage.textContent = message;
        popupMessage.className = `popup-message ${type}`;
        popupMessage.style.display = 'block';
        setTimeout(() => {
            popupMessage.style.display = 'none';
        }, 3000);
    }

    function refreshItems() {
        const categoryId = '<?php echo $encrypted_category_id; ?>';
        fetch('fetch_items.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ category_id: categoryId })
        })
        .then(response => response.json())
        .then(data => {
            if (Array.isArray(data)) {
                const itemsTable = document.getElementById('itemsTable');
                itemsTable.innerHTML = '';
                data.forEach(item => {
                    const row = document.createElement('tr');
                    row.id = `item-${item.item_id}`;
                    row.innerHTML = `
                        <td class='checkbox-cell'><input type='checkbox' name='selected_items[]' value='${item.item_id}'></td>
                        <!-- Changed: call new choosePriceAndPrint() -->
                        <td>
                            <img src='icons/printer.png' style='width:24px; height:24px; cursor:pointer;' 
                            onclick='event.stopPropagation(); choosePriceAndPrint("${item.name}", "${item.barcode}", "${item.price1}", "${item.price2}", "${item.price3}", "${item.price4}")' alt='طباعة' />
                        </td>
                        <td>${item.name}</td>
                        <td>${item.cost}</td>
                        <td>${item.price1}</td>
                        <td>${item.price2}</td>
                        <td>${item.price3}</td>
                        <td>${item.price4}</td>
                        <td>
                            <i class='fas fa-eye' style='color:#4A9AC4; cursor:pointer; font-size:18px;' onclick='event.stopPropagation(); showDescription("${item.name}", "${item.description}")'></i>
                        </td>
                        <td>${item.barcode}</td>
                        <!-- Removed old sticker button cell -->
                        <td>${item.quantity}</td>
                        <td onclick='event.stopPropagation()'>
                            <button class='toggle-btn ${item.item_use ? '' : 'inactive'}' type='button' onclick='toggleItemStatus(${item.item_id}, ${item.item_use})'>${item.item_use ? 'نشط' : 'متوقف'}</button>
                        </td>
                        <td onclick='event.stopPropagation()' style='text-align:center;'>
                            <i class='fas fa-edit' style='color:#007bff; cursor:pointer; font-size:18px; margin:0 8px;' onclick='editItem("${item.encrypted_item_id}")'></i>
                            <i class='fas fa-trash-alt' style='color:red; cursor:pointer; font-size:18px; margin:0 8px;' onclick='deleteItem(${item.item_id})'></i>
                        </td>
                    `;
                    itemsTable.appendChild(row);
                });
            } else {
                console.error('Unexpected response format:', data);
            }
        })
        .catch(error => {
            console.error('Error fetching items:', error);
        });
    }

    function openAddItemModal() {
        document.getElementById('addItemModal').style.display = 'block';
    }

    function closeAddItemModal() {
        document.getElementById('addItemModal').style.display = 'none';
    }

    document.querySelector('#addItemModal form').addEventListener('submit', function(event) {
        event.preventDefault();
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true; // Disable the submit button
        const formData = new FormData(this);
        fetch('add_item.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            Swal.fire({
                icon: data.success ? 'success' : 'error',
                title: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                if (data.success) {
                    refreshItems();
                    document.querySelector('#addItemModal form').reset();
                    document.getElementById('addItemModal').style.display = 'none';
                    // Refresh the page to display the new item
                    window.location.reload();
                }
                submitButton.disabled = false;
            });
        })
        .catch(error => {
            console.error('Error:', error);
            submitButton.disabled = false; // Re-enable the submit button in case of error
        });
    });

    document.querySelector('#editItemModal form').addEventListener('submit', function(event) {
        event.preventDefault();
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        const formData = new FormData(this);
        fetch('edit_item.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            Swal.fire({
                icon: data.success ? 'success' : 'error',
                title: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                if (data.success) {
                    refreshItems();
                    document.querySelector('#editItemModal form').reset();
                    document.getElementById('editItemModal').style.display = 'none';
                    // Refresh the page to display the updated item
                    window.location.reload();
                }
            submitButton.disabled = false; // Re-enable the submit button
            });
        })
        .catch(error => {
            console.error('Error:', error);
            submitButton.disabled = false; // Re-enable the submit button in case of error
        });
    });
    document.getElementById('deleteForm').addEventListener('submit', function(event) {
        event.preventDefault();
         Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، احذفها!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
        const formData = new FormData(this);
        fetch('delete_selected_items.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            if (data.includes('تم حذف الأصناف المحددة بنجاح')) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم حذف الأصناف المحددة بنجاح',
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    refreshItems();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'لا يمكن حذف الأصناف المحددة',
                    text: 'لا يمكن حذف الأصناف المحددة لأنها مرتبطة ببيانات أخرى.'
                
                });
                    }
                });
            }
        });
    });

    function toggleDiscountInput() {
        var discountContainer = document.getElementById('discount_input_container');
        discountContainer.style.display = discountContainer.style.display === 'none' ? 'block' : 'none';
    }

    document.addEventListener("DOMContentLoaded", function() {
        var barcodeInput = document.getElementById("barcode");
        var autoGenerateCheckbox = document.getElementById("auto_generate_barcode");

        // Disable barcode input by default
        barcodeInput.disabled = autoGenerateCheckbox.checked;

        // Toggle barcode input state based on checkbox
        autoGenerateCheckbox.addEventListener("change", function() {
            barcodeInput.disabled = this.checked;
            if (this.checked) {
                barcodeInput.value = ""; // Clear the field when auto-generate is enabled
            }
        });
    });

    function showDescription(name, description) {
        document.getElementById('itemName').textContent = name;
        document.getElementById('descriptionText').textContent = description;
        document.getElementById('descriptionModal').style.display = 'block';
    }

    function closeDescriptionModal() {
        document.getElementById('descriptionModal').style.display = 'none';
    }

    window.onclick = function(event) {
        const modal = document.getElementById('descriptionModal');
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    }

    document.querySelectorAll('.editable').forEach(cell => {
        cell.addEventListener('dblclick', function() {
            const field = this.dataset.field;
            const id = this.dataset.id;
            const originalValue = this.textContent.trim();
            const input = document.createElement('input');
            input.type = 'text';
            input.value = originalValue;
            input.classList.add('editable-input');
            // Define a revert handler to restore the original value on blur
            const revert = () => { cell.textContent = originalValue; }
            input.addEventListener('blur', revert);
            input.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    // Remove the blur event so it doesn't trigger revert
                    input.removeEventListener('blur', revert);
                    const newValue = this.value.trim();
                    if (newValue !== originalValue) {
                        updateItemField(id, field, newValue, cell, originalValue);
                    } else {
                        cell.textContent = originalValue;
                    }
                }
            });
            cell.textContent = '';
            cell.appendChild(input);
            input.focus();
        });
    });

    function updateItemField(id, field, value, cell, originalValue) {
        const formData = new FormData();
        formData.append('item_id', id);
        formData.append('field', field);
        formData.append('value', value);
        fetch('update_item_field.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                cell.textContent = value;
                Swal.fire({
                    icon: 'success',
                    title: 'تم تحديث الصنف بنجاح',
                    showConfirmButton: false,
                    timer: 1500
                });
            } else {
                cell.textContent = originalValue;
                Swal.fire({
                    icon: 'error',
                    title: 'فشل في تحديث الصنف',
                    text: data.message || 'حدث خطأ أثناء تحديث الصنف. حاول مرة أخرى.'
                });
            }
        })
        .catch(error => {
            console.error('Error updating item field:', error);
            cell.textContent = originalValue;
        });
    }

    // دوال الاستيكر المحدثة للنظام الموحد

    // دالة إغلاق مودال الاستيكر
    function closeStickerModal() {
        const modal = document.getElementById("stickerModal");
        if (modal) {
            modal.style.display = "none";
        }
    }

    // دالة طباعة الاستيكر المحدثة
    function printSticker() {
        if (typeof window.StickerSystem !== 'undefined' && window.StickerSystem.printSticker) {
            window.StickerSystem.printSticker();
        } else {
            // استخدام الدالة القديمة كبديل
            if (typeof window.printSticker === 'function') {
                window.printSticker();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'نظام الطباعة غير متاح'
                });
            }
        }
    }

    // تحديث دالة اختيار السعر لاستخدام النظام الموحد
    function choosePriceAndPrint(itemName, barcodeValue, price1, price2, price3, price4) {
        if (typeof window.StickerSystem !== 'undefined' && window.StickerSystem.choosePriceAndPrint) {
            window.StickerSystem.choosePriceAndPrint(itemName, barcodeValue, price1, price2, price3, price4);
        } else {
            // استخدام الدالة القديمة كبديل
            if (typeof window.choosePriceAndPrint === 'function') {
                window.choosePriceAndPrint(itemName, barcodeValue, price1, price2, price3, price4);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'نظام الاستيكر غير متاح'
                });
            }
        }
    }

    // جعل الدوال متاحة عالمياً
    window.closeStickerModal = closeStickerModal;
    window.printSticker = printSticker;
    window.choosePriceAndPrint = choosePriceAndPrint;
</script>
<style>
    .popup-message {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        display: none;
        z-index: 1000;
    }

    .popup-message.success {
        background-color: #4caf50;
        color: #ffffff;
    }

    .popup-message.error {
        background-color: #f44336;
        color: #ffffff;
    }

    .popup-message.edit-success {
        background-color: #ff9800;
        color: #ffffff;
    }

    /* Items Action Bar Styles */
    .items-action-bar {
        position: fixed;
        bottom: 60px; /* مسافة من شريط الفوتر */
        left: 0;
        right: 0;
        background: var(--color-secondary);
        border-top: 2px solid var(--color-primary);
        box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.1);
        padding: 15px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
        z-index: 2000;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    /* Dark Mode Support */
    [data-theme="dark"] .items-action-bar {
        background: var(--color-secondary);
        border-top-color: var(--color-primary);
        box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.3);
    }

    /* Action Bar Buttons */
    .action-bar-btn {
        background: var(--color-primary);
        color: var(--color-button-text);
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-family: 'Cairo', sans-serif;
        min-width: 160px;
        justify-content: center;
    }

    .action-bar-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        background: rgba(255, 204, 0, 0.9);
    }

    .action-bar-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* Specific Button Colors */
    .print-btn {
        background: #2196F3;
        color: white;
    }

    .print-btn:hover {
        background: #1976D2;
    }

    .activate-btn {
        background: #28a745;
        color: white;
    }

    .activate-btn:hover {
        background: #218838;
    }

    .deactivate-btn {
        background: #dc3545;
        color: white;
    }

    .deactivate-btn:hover {
        background: #c82333;
    }

    /* Dark Mode Button Adjustments */
    [data-theme="dark"] .action-bar-btn {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    [data-theme="dark"] .action-bar-btn:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .items-action-bar {
            flex-direction: column;
            gap: 10px;
            padding: 12px 15px;
            bottom: 50px; /* تقليل المسافة على الشاشات الصغيرة */
        }

        .action-bar-btn {
            width: 100%;
            max-width: 280px;
            padding: 10px 16px;
            font-size: 13px;
            min-width: auto;
        }
    }

    @media (max-width: 480px) {
        .items-action-bar {
            bottom: 45px;
            padding: 10px 12px;
        }

        .action-bar-btn {
            padding: 8px 14px;
            font-size: 12px;
            gap: 6px;
        }

        .action-bar-btn i {
            font-size: 14px;
        }
    }

    /* Animation for when buttons are clicked */
    .action-bar-btn.clicked {
        animation: buttonPulse 0.3s ease;
    }

    @keyframes buttonPulse {
        0% { transform: scale(1); }
        50% { transform: scale(0.95); }
        100% { transform: scale(1); }
    }

    /* Ensure content doesn't get hidden behind the action bar */
    body {
        padding-bottom: 120px; /* مساحة إضافية لتجنب تداخل المحتوى */
    }

    @media (max-width: 768px) {
        body {
            padding-bottom: 140px;
        }
    }
</style>

</body>
</html>

<?php
$conn->close();
?>
