<?php
require_once 'sticker_config.php';
checkPagePermission('sticker_settings', 'view');

$settings = $stickerConfig->getAllSettings();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات الاستيكر</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="sticker_styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }
        
        .settings-section {
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .settings-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #34495e;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .preview-section {
            grid-column: span 2;
            text-align: center;
        }
        
        .sticker-preview-container {
            display: inline-block;
            margin: 20px;
            padding: 20px;
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .preview-section {
                grid-column: span 1;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    
    <div class="container">
        <div class="settings-container">
            <h1><i class="fas fa-cog"></i> إعدادات الاستيكر</h1>
            
            <form id="settingsForm">
                <div class="settings-grid">
                    <!-- إعدادات الأبعاد -->
                    <div class="settings-section">
                        <h3><i class="fas fa-ruler"></i> الأبعاد</h3>
                        
                        <div class="form-group">
                            <label for="width">العرض:</label>
                            <input type="text" id="width" name="width" value="<?php echo htmlspecialchars($settings['width']); ?>" placeholder="مثال: 60mm">
                        </div>
                        
                        <div class="form-group">
                            <label for="height">الارتفاع:</label>
                            <input type="text" id="height" name="height" value="<?php echo htmlspecialchars($settings['height']); ?>" placeholder="مثال: 35mm">
                        </div>
                    </div>
                    
                    <!-- إعدادات المظهر -->
                    <div class="settings-section">
                        <h3><i class="fas fa-palette"></i> المظهر</h3>
                        
                        <div class="form-group">
                            <label for="background_image">صورة الخلفية:</label>
                            <input type="text" id="background_image" name="background_image" value="<?php echo htmlspecialchars($settings['background_image']); ?>" placeholder="/path/to/image.png">
                        </div>
                        
                        <div class="form-group">
                            <label for="brand_name">اسم العلامة التجارية:</label>
                            <input type="text" id="brand_name" name="brand_name" value="<?php echo htmlspecialchars($settings['brand_name']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="font_family">نوع الخط:</label>
                            <select id="font_family" name="font_family">
                                <option value="Cairo, Arial, sans-serif" <?php echo $settings['font_family'] === 'Cairo, Arial, sans-serif' ? 'selected' : ''; ?>>Cairo</option>
                                <option value="Arial, sans-serif" <?php echo $settings['font_family'] === 'Arial, sans-serif' ? 'selected' : ''; ?>>Arial</option>
                                <option value="Tahoma, sans-serif" <?php echo $settings['font_family'] === 'Tahoma, sans-serif' ? 'selected' : ''; ?>>Tahoma</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- إعدادات الباركود -->
                    <div class="settings-section">
                        <h3><i class="fas fa-barcode"></i> الباركود</h3>
                        
                        <div class="form-group">
                            <label for="barcode_format">تنسيق الباركود:</label>
                            <select id="barcode_format" name="barcode_format">
                                <option value="CODE128" <?php echo $settings['barcode_format'] === 'CODE128' ? 'selected' : ''; ?>>CODE128</option>
                                <option value="CODE39" <?php echo $settings['barcode_format'] === 'CODE39' ? 'selected' : ''; ?>>CODE39</option>
                                <option value="EAN13" <?php echo $settings['barcode_format'] === 'EAN13' ? 'selected' : ''; ?>>EAN13</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="qr_size">حجم رمز QR:</label>
                            <input type="number" id="qr_size" name="qr_size" value="<?php echo htmlspecialchars($settings['qr_size']); ?>" min="20" max="200">
                        </div>
                    </div>
                    
                    <!-- إعدادات أخرى -->
                    <div class="settings-section">
                        <h3><i class="fas fa-cogs"></i> إعدادات أخرى</h3>
                        
                        <div class="form-group">
                            <label for="currency">رمز العملة:</label>
                            <input type="text" id="currency" name="currency" value="<?php echo htmlspecialchars($settings['currency']); ?>" placeholder="JOD">
                        </div>
                        
                        <div class="form-group">
                            <label for="print_quality">جودة الطباعة:</label>
                            <select id="print_quality" name="print_quality">
                                <option value="high" <?php echo $settings['print_quality'] === 'high' ? 'selected' : ''; ?>>عالية</option>
                                <option value="medium" <?php echo $settings['print_quality'] === 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                <option value="low" <?php echo $settings['print_quality'] === 'low' ? 'selected' : ''; ?>>منخفضة</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- معاينة الاستيكر -->
                    <div class="settings-section preview-section">
                        <h3><i class="fas fa-eye"></i> معاينة الاستيكر</h3>
                        
                        <div class="sticker-preview-container">
                            <div id="previewSticker" class="sticker-preview">
                                <div class="sticker-content" style="background-image: url('<?php echo htmlspecialchars($settings['background_image']); ?>');">
                                    <div class="sticker-qr-code"></div>
                                    <div class="sticker-brand-name"><?php echo htmlspecialchars($settings['brand_name']); ?></div>
                                    <div class="sticker-product-name">منتج تجريبي</div>
                                    <div class="sticker-price">25.00 <?php echo htmlspecialchars($settings['currency']); ?></div>
                                    <div class="sticker-barcode-container">
                                        <svg id="previewBarcode"></svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="updatePreview()">
                            <i class="fas fa-sync"></i> تحديث المعاينة
                        </button>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                    
                    <button type="button" class="btn btn-warning" onclick="exportSettings()">
                        <i class="fas fa-download"></i> تصدير الإعدادات
                    </button>
                    
                    <button type="button" class="btn btn-danger" onclick="resetSettings()">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script>
        // تحديث المعاينة
        function updatePreview() {
            const previewSticker = document.getElementById('previewSticker');
            const width = document.getElementById('width').value;
            const height = document.getElementById('height').value;
            const backgroundImage = document.getElementById('background_image').value;
            const brandName = document.getElementById('brand_name').value;
            const currency = document.getElementById('currency').value;
            
            // تحديث الأبعاد
            previewSticker.style.width = width;
            previewSticker.style.height = height;
            
            // تحديث الخلفية
            const stickerContent = previewSticker.querySelector('.sticker-content');
            stickerContent.style.backgroundImage = `url('${backgroundImage}')`;
            
            // تحديث النصوص
            previewSticker.querySelector('.sticker-brand-name').textContent = brandName;
            previewSticker.querySelector('.sticker-price').textContent = `25.00 ${currency}`;
            
            // تحديث الباركود
            try {
                JsBarcode("#previewBarcode", "1234567890", {
                    format: document.getElementById('barcode_format').value,
                    width: 2,
                    height: 30,
                    displayValue: false
                });
            } catch (error) {
                console.error('خطأ في توليد الباركود:', error);
            }
        }
        
        // حفظ الإعدادات
        document.getElementById('settingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const settings = {};
            
            for (let [key, value] of formData.entries()) {
                settings[key] = value;
            }
            
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_settings&settings=${encodeURIComponent(JSON.stringify(settings))}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'تم الحفظ',
                        text: data.message,
                        timer: 2000
                    });
                    updatePreview();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ',
                        text: data.errors ? data.errors.join('\n') : 'حدث خطأ غير متوقع'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ',
                    text: 'حدث خطأ في الاتصال'
                });
            });
        });
        
        // تصدير الإعدادات
        function exportSettings() {
            fetch('sticker_config.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_settings'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const blob = new Blob([JSON.stringify(data.settings, null, 2)], {type: 'application/json'});
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'sticker_settings.json';
                    a.click();
                    URL.revokeObjectURL(url);
                }
            });
        }
        
        // إعادة تعيين الإعدادات
        function resetSettings() {
            Swal.fire({
                title: 'هل أنت متأكد؟',
                text: 'سيتم إعادة تعيين جميع الإعدادات للقيم الافتراضية',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، إعادة تعيين',
                cancelButtonText: 'إلغاء'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch('sticker_config.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=reset_settings'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم إعادة التعيين',
                                text: data.message,
                                timer: 2000
                            }).then(() => {
                                location.reload();
                            });
                        }
                    });
                }
            });
        }
        
        // تحديث المعاينة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updatePreview();
        });
    </script>
</body>
</html>
