/**
 * ملف تهيئة نظام الاستيكر
 * Sticker System Initialization
 */

(function() {
    'use strict';
    
    // تحميل إعدادات الاستيكر من الخادم
    function loadStickerSettings() {
        return fetch('sticker_config.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_settings'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث إعدادات النظام
                if (window.StickerSystem && window.StickerSystem.STICKER_CONFIG) {
                    Object.assign(window.StickerSystem.STICKER_CONFIG, data.settings);
                }
                return data.settings;
            } else {
                console.warn('فشل في تحميل إعدادات الاستيكر، سيتم استخدام الإعدادات الافتراضية');
                return null;
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل إعدادات الاستيكر:', error);
            return null;
        });
    }
    
    // تطبيق الإعدادات على CSS
    function applyStickerSettings(settings) {
        if (!settings) return;
        
        // إنشاء أو تحديث متغيرات CSS
        const root = document.documentElement;
        
        if (settings.width) {
            root.style.setProperty('--sticker-width', settings.width);
        }
        
        if (settings.height) {
            root.style.setProperty('--sticker-height', settings.height);
        }
        
        if (settings.font_family) {
            root.style.setProperty('--sticker-font-family', settings.font_family);
        }
        
        // تحديث الخلفية إذا كانت موجودة
        if (settings.background_image) {
            const stickerPreviews = document.querySelectorAll('.sticker-preview .sticker-content');
            stickerPreviews.forEach(preview => {
                preview.style.backgroundImage = `url('${settings.background_image}')`;
            });
        }
    }
    
    // تهيئة النظام عند تحميل الصفحة
    function initializeStickerSystem() {
        // التحقق من وجود النظام
        if (typeof window.StickerSystem === 'undefined') {
            console.warn('نظام الاستيكر غير محمل');
            return;
        }
        
        // تحميل الإعدادات وتطبيقها
        loadStickerSettings().then(settings => {
            if (settings) {
                applyStickerSettings(settings);
                console.log('تم تحميل إعدادات الاستيكر بنجاح');
            }
        });
        
        // إضافة مستمعات الأحداث
        setupEventListeners();
    }
    
    // إعداد مستمعات الأحداث
    function setupEventListeners() {
        // مستمع لتحديث الإعدادات
        document.addEventListener('stickerSettingsUpdated', function(event) {
            if (event.detail && event.detail.settings) {
                applyStickerSettings(event.detail.settings);
            }
        });
        
        // مستمع لإعادة تحميل الإعدادات
        document.addEventListener('reloadStickerSettings', function() {
            loadStickerSettings().then(settings => {
                if (settings) {
                    applyStickerSettings(settings);
                }
            });
        });
    }
    
    // دالة لتحديث إعدادات الاستيكر من خارج النظام
    window.updateStickerSettings = function(newSettings) {
        return fetch('sticker_config.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=update_settings&settings=${encodeURIComponent(JSON.stringify(newSettings))}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                applyStickerSettings(newSettings);
                
                // إرسال حدث التحديث
                const event = new CustomEvent('stickerSettingsUpdated', {
                    detail: { settings: newSettings }
                });
                document.dispatchEvent(event);
                
                return { success: true, message: 'تم تحديث الإعدادات بنجاح' };
            } else {
                return data;
            }
        });
    };
    
    // دالة للحصول على الإعدادات الحالية
    window.getStickerSettings = function() {
        return loadStickerSettings();
    };
    
    // دالة لإعادة تعيين الإعدادات
    window.resetStickerSettings = function() {
        return fetch('sticker_config.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=reset_settings'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل الصفحة لتطبيق الإعدادات الافتراضية
                setTimeout(() => location.reload(), 1000);
            }
            return data;
        });
    };
    
    // تشغيل التهيئة عند تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeStickerSystem);
    } else {
        initializeStickerSystem();
    }
    
    // تشغيل التهيئة عند تحميل النافذة (للتأكد)
    window.addEventListener('load', function() {
        // تأخير بسيط للتأكد من تحميل جميع الملفات
        setTimeout(initializeStickerSystem, 100);
    });
    
})();

/**
 * دوال مساعدة للتكامل مع النظام
 */

// دالة لعرض رسالة تأكيد عند تحديث الإعدادات
function showStickerSettingsUpdateMessage(success, message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: success ? 'success' : 'error',
            title: success ? 'تم التحديث' : 'خطأ',
            text: message,
            timer: success ? 2000 : 0,
            showConfirmButton: !success
        });
    } else {
        alert(message);
    }
}

// دالة لتصدير الإعدادات
function exportStickerSettings() {
    window.getStickerSettings().then(settings => {
        if (settings) {
            const blob = new Blob([JSON.stringify(settings, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sticker_settings_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    });
}

// دالة لاستيراد الإعدادات
function importStickerSettings(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const settings = JSON.parse(e.target.result);
                window.updateStickerSettings(settings).then(result => {
                    if (result.success) {
                        showStickerSettingsUpdateMessage(true, 'تم استيراد الإعدادات بنجاح');
                        resolve(result);
                    } else {
                        showStickerSettingsUpdateMessage(false, result.message || 'فشل في استيراد الإعدادات');
                        reject(result);
                    }
                });
            } catch (error) {
                const message = 'ملف الإعدادات غير صحيح';
                showStickerSettingsUpdateMessage(false, message);
                reject({ success: false, message });
            }
        };
        reader.readAsText(file);
    });
}

// جعل الدوال متاحة عالمياً
window.exportStickerSettings = exportStickerSettings;
window.importStickerSettings = importStickerSettings;
window.showStickerSettingsUpdateMessage = showStickerSettingsUpdateMessage;
