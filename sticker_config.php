<?php
/**
 * إعدادات نظام الاستيكر
 * Sticker System Configuration
 */

require_once 'db_connection.php';
require_once 'security.php';
require_once 'encryption_functions.php';

class StickerConfig {
    private $conn;
    private $key;
    
    // إعدادات افتراضية للاستيكر
    private $defaultSettings = [
        'width' => '60mm',
        'height' => '35mm',
        'background_image' => 'Eyad.png', // مسار نسبي بدون شرطة مائلة
        'brand_name' => 'AN NAJJAR',
        'currency' => 'JOD',
        'font_family' => 'Cairo, Arial, sans-serif',
        'barcode_format' => 'CODE128',
        'qr_size' => 50,
        'print_quality' => 'high'
    ];
    
    public function __construct($database_connection, $encryption_key) {
        $this->conn = $database_connection;
        $this->key = $encryption_key;
        $this->initializeSettings();
    }
    
    /**
     * تهيئة إعدادات الاستيكر في قاعدة البيانات
     */
    private function initializeSettings() {
        // التحقق من وجود جدول الإعدادات
        $tableExists = $this->conn->query("SHOW TABLES LIKE 'sticker_settings'");
        
        if ($tableExists->num_rows == 0) {
            $this->createSettingsTable();
            $this->insertDefaultSettings();
        }
    }
    
    /**
     * إنشاء جدول إعدادات الاستيكر
     */
    private function createSettingsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS sticker_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if (!$this->conn->query($sql)) {
            error_log("خطأ في إنشاء جدول إعدادات الاستيكر: " . $this->conn->error);
        }
    }
    
    /**
     * إدراج الإعدادات الافتراضية
     */
    private function insertDefaultSettings() {
        $stmt = $this->conn->prepare("INSERT INTO sticker_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
        
        $descriptions = [
            'width' => 'عرض الاستيكر',
            'height' => 'ارتفاع الاستيكر',
            'background_image' => 'صورة خلفية الاستيكر',
            'brand_name' => 'اسم العلامة التجارية',
            'currency' => 'رمز العملة',
            'font_family' => 'نوع الخط',
            'barcode_format' => 'تنسيق الباركود',
            'qr_size' => 'حجم رمز QR',
            'print_quality' => 'جودة الطباعة'
        ];
        
        foreach ($this->defaultSettings as $key => $value) {
            $description = $descriptions[$key] ?? '';
            $stmt->bind_param("sss", $key, $value, $description);
            $stmt->execute();
        }
        
        $stmt->close();
    }
    
    /**
     * الحصول على إعداد محدد
     */
    public function getSetting($key, $default = null) {
        $stmt = $this->conn->prepare("SELECT setting_value FROM sticker_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $stmt->close();
            return $row['setting_value'];
        }
        
        $stmt->close();
        return $default ?? ($this->defaultSettings[$key] ?? null);
    }
    
    /**
     * تحديث إعداد محدد
     */
    public function updateSetting($key, $value) {
        $stmt = $this->conn->prepare("UPDATE sticker_settings SET setting_value = ?, updated_at = CURRENT_TIMESTAMP WHERE setting_key = ?");
        $stmt->bind_param("ss", $value, $key);
        $success = $stmt->execute();
        $stmt->close();
        
        return $success;
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getAllSettings() {
        $settings = [];
        $result = $this->conn->query("SELECT setting_key, setting_value FROM sticker_settings");
        
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        // دمج الإعدادات الافتراضية للمفاتيح المفقودة
        return array_merge($this->defaultSettings, $settings);
    }
    
    /**
     * تصدير الإعدادات كـ JSON
     */
    public function exportSettingsAsJson() {
        return json_encode($this->getAllSettings(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    /**
     * تصدير الإعدادات كـ JavaScript
     */
    public function exportSettingsAsJS() {
        $settings = $this->getAllSettings();
        return "const STICKER_CONFIG = " . json_encode($settings, JSON_UNESCAPED_UNICODE) . ";";
    }
    
    /**
     * التحقق من صحة الإعدادات
     */
    public function validateSettings($settings) {
        $errors = [];
        
        // التحقق من الأبعاد
        if (isset($settings['width']) && !preg_match('/^\d+(\.\d+)?(mm|px|cm)$/', $settings['width'])) {
            $errors[] = 'عرض الاستيكر غير صحيح';
        }
        
        if (isset($settings['height']) && !preg_match('/^\d+(\.\d+)?(mm|px|cm)$/', $settings['height'])) {
            $errors[] = 'ارتفاع الاستيكر غير صحيح';
        }
        
        // التحقق من صورة الخلفية (تم تعطيل التحقق مؤقتاً لحل مشكلة المسارات)
        if (isset($settings['background_image']) && !empty($settings['background_image'])) {
            $imagePath = $settings['background_image'];

            // التحقق من أن المسار ليس فارغاً وليس يحتوي على أحرف خطيرة
            if (strpos($imagePath, '..') !== false || strpos($imagePath, '<') !== false || strpos($imagePath, '>') !== false) {
                $errors[] = 'مسار صورة الخلفية غير آمن';
            }

            // تسجيل المسار في السجل للمراجعة
            error_log("Background image path: " . $imagePath);
        }
        
        // التحقق من حجم QR
        if (isset($settings['qr_size']) && (!is_numeric($settings['qr_size']) || $settings['qr_size'] < 20 || $settings['qr_size'] > 200)) {
            $errors[] = 'حجم رمز QR يجب أن يكون بين 20 و 200';
        }
        
        return $errors;
    }
    
    /**
     * حفظ إعدادات متعددة
     */
    public function saveMultipleSettings($settings) {
        $errors = $this->validateSettings($settings);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        $this->conn->begin_transaction();
        
        try {
            foreach ($settings as $key => $value) {
                if (!$this->updateSetting($key, $value)) {
                    throw new Exception("فشل في تحديث الإعداد: $key");
                }
            }
            
            $this->conn->commit();
            return ['success' => true, 'message' => 'تم حفظ الإعدادات بنجاح'];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return ['success' => false, 'errors' => [$e->getMessage()]];
        }
    }
    
    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function resetToDefaults() {
        $this->conn->query("DELETE FROM sticker_settings");
        $this->insertDefaultSettings();
        return ['success' => true, 'message' => 'تم إعادة تعيين الإعدادات للقيم الافتراضية'];
    }
}

// معالجة طلبات AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $key = getenv('ENCRYPTION_KEY');
    $stickerConfig = new StickerConfig($conn, $key);
    
    switch ($_POST['action']) {
        case 'get_settings':
            echo json_encode(['success' => true, 'settings' => $stickerConfig->getAllSettings()]);
            break;
            
        case 'update_settings':
            $settings = json_decode($_POST['settings'], true);
            $result = $stickerConfig->saveMultipleSettings($settings);
            echo json_encode($result);
            break;
            
        case 'reset_settings':
            $result = $stickerConfig->resetToDefaults();
            echo json_encode($result);
            break;
            
        case 'export_js':
            echo $stickerConfig->exportSettingsAsJS();
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
    }
    exit;
}

// إنشاء مثيل عام للاستخدام في الملفات الأخرى
if (!isset($stickerConfig)) {
    $key = getenv('ENCRYPTION_KEY');
    $stickerConfig = new StickerConfig($conn, $key);
}
?>
