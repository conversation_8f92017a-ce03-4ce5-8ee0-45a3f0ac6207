let initialLoad = true;
let previousOrderCounts = {};
let isFiltering = false;
let newOrderPlayed = false;
let suppressSound = false; // Flag to ignore sound when changing filter
let firstSSE = true; // Variable to ignore the first SSE message
let audioEnabled = false;
let modalOpen = false;
let lastUpdate = 0;
let sseReconnectTimer;

document.getElementById('status-filter').addEventListener('change', () => {
    const statusValue = document.getElementById('status-filter').value;
    if (statusValue === "") {
        // When switching back to "All statuses", set the flag to ignore sound
        suppressSound = true;
        isFiltering = false;
        fetchOrders(false);
    } else {
        isFiltering = true;
        fetchOrders(false);
    }
});

document.getElementById('start-date').addEventListener('change', () => {
    suppressSound = true;
    fetchOrders(false);
});

document.getElementById('end-date').addEventListener('change', () => {
    suppressSound = true;
    fetchOrders(false);
});

document.getElementById('search-name').addEventListener('input', () => {
    suppressSound = true;
});

document.getElementById('search-phone').addEventListener('input', () => {
    suppressSound = true;
    fetchOrders(false);
});

function searchOrders() {
    suppressSound = true;
    fetchOrders(false);
}

function clearDates() {
    document.getElementById('start-date').value = '';
    document.getElementById('end-date').value = '';
    suppressSound = true;
    fetchOrders(false);
}

function fetchOrders(playSound = true) {
    // حفظ حالة الاختيار الحالية قبل مسح الجدول
    let selectedOrders = {};
    document.querySelectorAll('.order-checkbox:checked').forEach(chk => {
        const key = chk.getAttribute('data-plain-account-id') + '_' + chk.getAttribute('data-order-date');
        selectedOrders[key] = true;
    });
    
    const now = Date.now();
    if (now - lastUpdate < 2000) return; // Prevent updates faster than 2 sec
    lastUpdate = now;
    
    const searchName = document.getElementById('search-name').value;
    const searchPhone = document.getElementById('search-phone').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const statusFilter = document.getElementById('status-filter').value;
    const displayMode = document.getElementById('display-mode').value; // New display mode value
    // New barcode search value
    const searchBarcode = document.getElementById('search-barcode').value;
    const storeId = document.getElementById('store-id').value;

    const params = new URLSearchParams();
    if (searchName) params.append('search_name', searchName);
    if (searchPhone) params.append('search_phone', searchPhone);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (statusFilter) params.append('status', statusFilter);
    if (searchBarcode) params.append('search_barcode', searchBarcode);
    if (storeId) params.append('store_id', storeId);
    if (displayMode) params.append('display_mode', displayMode); // Append display mode

    fetch(`get_orders.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            const ordersTableBody = document.getElementById('orders-table-body');
            const previousOrderCount = ordersTableBody.children.length;
            ordersTableBody.innerHTML = '';

            const isFilterActive = statusFilter !== "" || startDate !== "" || endDate !== "" || searchName !== "" || searchPhone !== "";

            let hasRealChanges = false;
            data.forEach(order => {
                const encryptedAccountId = order.encrypted_account_id;
                const plainAccountId = order.account_id; // استخدم المعرف العادي للمقارنة
                const key = plainAccountId + '_' + order.order_date;
                const isChecked = selectedOrders[key] ? 'checked' : '';
                const status = order.status === 'pending' 
                    ? '<span class="status-frame pending">انتظار</span>' 
                    : order.status === 'delayed' 
                        ? '<span class="status-frame delayed">مؤجل</span>' 
                        : '<span class="status-frame confirmed">مؤكد</span>';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                      <input type="checkbox" 
                        class="order-checkbox" 
                        data-plain-account-id="${plainAccountId}" 
                        data-encrypted-account-id="${encryptedAccountId}" 
                        data-order-date="${order.order_date}" ${isChecked}>
                    </td>
                    <td>
                      <img src="icons/printer.png" alt="Print" class="printer-icon" onclick='showInvoiceOptions("${encryptedAccountId}", "${order.order_date}")'>
                    </td>
                    <td class="username" data-account-id="${plainAccountId}" onclick='showUserOrders("${plainAccountId}")'>${order.username}</td>
                    <td>${order.phone}</td>
                    <td>${status}</td>
                    <td>${order.total_quantity}</td>
                    <td>${order.collected}</td>
                    <td>${order.total_amount}</td>
                    <td>${order.remaining}</td>
                    <td>${order.order_date || '-'}</td>
                    <td>
                      <img src="icons/eye.png" alt="View" class="eye-icon" onclick='showOrderDetails("${encryptedAccountId}", "${order.order_date}", false)'>
                      <img src="icons/delete.png" alt="Delete" class="delete-icon icon-spacing" onclick='deleteOrder("${encryptedAccountId}", "${order.order_date}", this)'>
                    </td>
                `;
                ordersTableBody.appendChild(row);

                const previousCount = previousOrderCounts[order.order_date] || 0;
                if (order.total_quantity > previousCount) {
                    hasRealChanges = true;
                }
                previousOrderCounts[order.order_date] = order.total_quantity;
            });

            if (hasRealChanges && !isFilterActive && !suppressSound && !modalOpen && playSound) {
                window.playNewOrderSound();
            }

            initialLoad = false;
            isFiltering = false;
            suppressSound = false;
        })
        .catch(error => console.error('Error fetching orders:', error));
}

function clearDates() {
    document.getElementById('start-date').value = '';
    document.getElementById('end-date').value = '';
}

function showOrderDetails(encryptedAccountId, orderDate = null, playSound = true) {
    modalOpen = true;

    // Construct the URL based on whether `orderDate` is provided
    const url = orderDate && orderDate !== 'undefined'
        ? `get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}`
        : `get_order_details.php?account_id=${encodeURIComponent(encryptedAccountId)}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (!data || data.length === 0) {
                console.error('Error fetching order details: No data received');
                return;
            }

            const orderDetailsBody = document.getElementById('order-details-body');
            orderDetailsBody.innerHTML = '';
            orderDetailsBody.dataset.accountId = encryptedAccountId; // Store account ID in dataset
            if (orderDate && orderDate !== 'undefined') {
                orderDetailsBody.dataset.orderDate = orderDate; // Store order_date in dataset
            }

            data.forEach(item => {
                const status = item.status === 'pending' 
                    ? '<span class="status-frame pending">انتظار</span>' 
                    : item.status === 'delayed' 
                        ? '<span class="status-frame delayed">مؤجل</span>' 
                        : '<span class="status-frame confirmed">مؤكد</span>';
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="checkbox" class="order-detail-checkbox" data-sale-id="${item.sale_id}"></td>
                    <td class="sale_id">${item.sale_id}</td>
                    <td class="name">${item.name}</td>
                    <td class="quantity" contenteditable="true" onkeypress="handleQuantityKeyPress(event, this, ${item.sale_id}, '${item.status}', ${item.item_id})">${item.quantity}</td>
                    <td class="returned" contenteditable="true" onkeypress="handleReturnedKeyPress(event, this, ${item.sale_id})" data-old-returned="${item.returned}">${item.returned}</td>
                    <td class="price" contenteditable="true" onkeypress="handlePriceKeyPress(event, this, ${item.sale_id})">${item.price}</td>
                    <td class="total_amount">${item.total_amount}</td>
                    <td class="collected" contenteditable="true" onkeypress="handleKeyPress(event, this, ${item.sale_id}, '${encryptedAccountId}', ${item.total_amount})" oninput="validateCollectedAmount(this, ${item.total_amount})">${item.collected}</td>
                    <td class="remaining">${item.total_amount - item.collected}</td>
                    <td>${status}</td>
                    <td>
                        ${item.collected < item.total_amount ? `<button class='collect-btn' onclick='markAsPaid(this, ${item.sale_id}, "${encryptedAccountId}")'></button>` : ''}
                        <img src="icons/delete_24.png" alt="Delete" style="cursor:pointer;" onclick='deleteOrderItem(${item.sale_id}, this)'>
                    </td>
                `;
                orderDetailsBody.appendChild(row);
            });

            document.getElementById('orderDetailsModal').style.display = 'block';
        })
        .catch(error => console.error('Error fetching order details:', error));
}

function markAsPaid(button, saleId, encryptedAccountId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، ادفعها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const row = button.closest('tr');
            const quantity = row.querySelector('.quantity').textContent.trim();
            const price = row.querySelector('.price').textContent.trim();
            const totalAmount = row.querySelector('.total_amount').textContent.trim();
            const collected = row.querySelector('.collected').textContent.trim();
            const remaining = totalAmount - collected;
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

            if (remaining <= 0) {
                console.error('This item has already been paid for.');
                return;
            }

            fetch('mark_as_paid.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ quantity: quantity, price: price, total_amount: totalAmount, sale_id: saleId })
            }).then(response => response.json())
              .then(data => {
                  if (data.status === 'success') {
                      showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                      fetchOrders(); // Refresh orders table
                  }
              })
              .catch(error => console.error('Error marking item as paid:', error));
        }
    });
}

function markAsDelayed(element, saleId, encryptedAccountId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، أجله!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate; // Correctly retrieve order_date

            fetch(`mark_as_delayed.php?sale_id=${saleId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showOrderDetails(encryptedAccountId, orderDate, false); // Pass the correct order_date
                        fetchOrders(); // Refresh orders table
                    }
                })
                .catch(error => console.error('Error marking item as delayed:', error));
        }
    });
}

function closeModal() {
    modalOpen = false;
    document.getElementById('orderDetailsModal').style.display = 'none';
}

window.onclick = function(event) {
    const modal = document.getElementById('orderDetailsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// Fetch orders on page load
document.addEventListener('DOMContentLoaded', () => {
    fetchOrders(false);
    initialLoad = false; // Set initialLoad to false after the first fetch
});

// Use Server-Sent Events (SSE) to update orders continuously
if (typeof(EventSource) !== "undefined") {
    const storeId = document.getElementById('store-id').value;
    const setupSSE = () => {
        const source = new EventSource(`sse_orders.php?store_id=${storeId}`);
        source.onmessage = (e) => {
            if (firstSSE) {
                firstSSE = false;
                return;
            }
            // Disable sound by passing false
            fetchOrders(false);
        };
        source.onerror = () => {
            source.close();
            clearTimeout(sseReconnectTimer);
            sseReconnectTimer = setTimeout(setupSSE, 5000);
        };
    };
    setupSSE();
} else {
    console.error("Your browser does not support Server-Sent Events.");
}

function deleteOrderFromModal() {
    const saleId = document.querySelector('#order-details-body .sale_id').textContent.trim();
    const encryptedAccountId = document.getElementById('store-id').value; // Assuming it's the store_id, adjust if needed
    deleteOrder(saleId, document.querySelector(`#orders-table-body tr[data-sale-id="${saleId}"]`), true);
    setTimeout(() => showOrderDetails(encryptedAccountId, saleId, false), 500); // Refresh order details after a short delay
}

function deleteOrder(accountId, orderDate, element, fromModal = false) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('delete_full_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ account_id: accountId, order_date: orderDate })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة الصف الخاص بالطلب المحذوف من واجهة المستخدم
                    if (element) {
                        element.closest('tr').remove();
                    }
                    // إذا كان الحذف من داخل المودال، تحقق من بقاء أصناف أخرى
                    if (fromModal) {
                        const orderDetailsBody = document.getElementById('order-details-body');
                        if (orderDetailsBody.children.length === 0) {
                            closeModal(); // إغلاق المودال إذا لم يتبقَ أي صنف
                        }
                    }
                    // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                    fetchOrders();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });
}

function payAllItems() {
    const orderDetailsBody = document.getElementById('order-details-body');
    const rows = orderDetailsBody.querySelectorAll('tr');
    const saleIds = [];
    const orderDate = orderDetailsBody.dataset.orderDate; // Get the order date
    const encryptedAccountId = orderDetailsBody.dataset.accountId; // Get the account ID of the order

    rows.forEach(row => {
        const saleId = row.querySelector('.sale_id').textContent.trim();
        const collected = row.querySelector('.collected').textContent.trim();

        if (collected == 0) {
            saleIds.push(saleId);
        }
    });

    if (saleIds.length > 0) {
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "لن تتمكن من التراجع عن هذا!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'نعم، ادفع الكل!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch('pay_all_items.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sale_ids: saleIds, account_id: encryptedAccountId, order_date: orderDate }) // Send account ID and order date
                }).then(response => response.json())
                  .then(data => {
                      if (data.status === 'success') {
                          // Update item statuses in the modal
                          rows.forEach(row => {
                              const collectedCell = row.querySelector('.collected');
                              const statusCell = row.querySelector('.status-frame');
                              if (collectedCell.textContent.trim() == 0) {
                                  collectedCell.textContent = row.querySelector('.total_amount').textContent.trim();
                                  statusCell.className = 'status-frame confirmed';
                                  statusCell.textContent = 'مؤكد';
                              }
                          });
                          fetchOrders(); // Refresh orders table
                          showOrderDetails(encryptedAccountId, orderDate, false); // Fetch order details with account ID and order date
                      } else {
                          alert(data.message);
                      }
                  })
                  .catch(error => console.error('Error marking all items as paid:', error));
            }
        });
    }
}

function deleteOrderItem(saleId, element) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، احذفها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('delete_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ order_id: saleId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة الصف الخاص بالصنف المحذوف من واجهة المستخدم
                    if (element) {
                        element.closest('tr').remove();
                    }
                    // تحديث قائمة الطلبات الرئيسية إن دعت الحاجة
                    fetchOrders();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });
}

function handleKeyPress(event, element, saleId, encryptedAccountId, totalAmount) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateCollectedAmount(element, saleId, encryptedAccountId, totalAmount);
    }
}

function validateCollectedAmount(element, totalAmount) {
    const collectedAmount = parseFloat(element.textContent.trim());
    if (collectedAmount > totalAmount) {
        element.textContent = totalAmount;
    }
}

function updateCollectedAmount(element, saleId, encryptedAccountId, totalAmount) {
    const collectedAmount = parseFloat(element.textContent.trim());
    if (collectedAmount > totalAmount) {
        alert('المدفوع لا يمكن أن يكون أكبر من مجموع السعر');
        element.textContent = totalAmount;
        return;
    }
    const orderDate = document.querySelector('#order-details-body').dataset.orderDate;

    fetch('update_collected_amount.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sale_id: saleId, collected: collectedAmount, total_amount: totalAmount })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showOrderDetails(encryptedAccountId, orderDate, false);
            fetchOrders();
        } else {
            alert(data.message);
        }
    })
    .catch(error => console.error('Error updating collected amount:', error));
}

function applyFilter() {
    isFiltering = true;
    fetchOrders(false);
}

// Add a global variable to track unlocked audio state
let audioUnlocked = false;
let lastAudioPlay = 0;

function playNewOrderSound() {
    // Only play if audio has been unlocked by user interaction
    if (!audioEnabled) {
        return;
    }
    const newOrderSound = document.getElementById('newOrderSound');
    const now = Date.now();
    if (now - lastAudioPlay < 5000) {
        return; // rate-limit playback (5 sec)
    }
    newOrderSound.play().then(() => {
        lastAudioPlay = Date.now();
    }).catch(error => {
        console.warn('Audio play prevented, user interaction required.', error);
    });
}

// One-time unlock on first user interaction – modified to prevent audible playback
document.addEventListener('click', function unlockAudio() {
    const newOrderSound = document.getElementById('newOrderSound');
    newOrderSound.play().then(() => {
        newOrderSound.pause();
        newOrderSound.currentTime = 0;
        audioEnabled = true;
        document.removeEventListener('click', unlockAudio);
    }).catch(() => { /* Ignore errors */ });
});

function generateInvoice(encryptedAccountId, orderDate) {
    window.location.href = `generate_invoice.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}`;
}

function showInvoiceOptions(encryptedAccountId, orderDate = null) {
    const displayMode = document.getElementById('display-mode').value; // Get the current display mode
    if (displayMode === 'by_account') {
        // Only show the "فاتورة A4(PDF)" option
        Swal.fire({
            title: 'اختر نوع الفاتورة',
            showCancelButton: true,
            confirmButtonText: 'فاتورة A4(PDF)',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const url = orderDate 
                    ? `generate_invoice.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}`
                    : `generate_invoice.php?account_id=${encodeURIComponent(encryptedAccountId)}`;
                window.location.href = url;
            }
        });
    } else {
        // Show both options for other modes
        Swal.fire({
            title: 'اختر نوع الفاتورة',
            showCancelButton: true,
            showDenyButton: true,
            confirmButtonText: 'فاتورة A4(PDF)',
            denyButtonText: 'ايصال',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                const url = orderDate 
                    ? `generate_invoice.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}`
                    : `generate_invoice.php?account_id=${encodeURIComponent(encryptedAccountId)}`;
                window.location.href = url;
            } else if (result.isDenied) {
                const url = orderDate 
                    ? `generate_invoice2.php?account_id=${encodeURIComponent(encryptedAccountId)}&order_date=${orderDate}`
                    : `generate_invoice2.php?account_id=${encodeURIComponent(encryptedAccountId)}`;
                window.location.href = url;
            }
        });
    }
}

function handlePriceKeyPress(event, element, saleId) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateItemPrice(element, saleId);
        element.blur(); // Remove focus from the input
    }
}

function updateItemPrice(element, saleId) {
    const newPrice = parseFloat(element.textContent.trim());
    if (isNaN(newPrice) || newPrice <= 0) {
        alert('السعر غير صالح');
        return;
    }

    fetch('update_item_price.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sale_id: saleId, price: newPrice })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const row = element.closest('tr');
            const quantity = parseFloat(row.querySelector('.quantity').textContent.trim());
            const totalAmount = newPrice * quantity;
            row.querySelector('.total_amount').textContent = totalAmount.toFixed(2);
            const collected = parseFloat(row.querySelector('.collected').textContent.trim());
            row.querySelector('.remaining').textContent = (totalAmount - collected).toFixed(2);
        } else {
            alert(data.message);
        }
    })
    .catch(error => console.error('Error updating item price:', error));
}

function handleQuantityKeyPress(event, element, saleId, status, itemId) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateItemQuantity(element, saleId, status, itemId);
        element.blur(); // Remove focus from the input
    }
}

function updateItemQuantity(element, saleId, status, itemId) {
    const newQuantity = parseInt(element.textContent.trim());
    if (isNaN(newQuantity) || newQuantity <= 0) {
        alert('الكمية غير صالحة');
        return;
    }

    fetch('update_item_quantity.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sale_id: saleId, quantity: newQuantity, status: status, item_id: itemId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const row = element.closest('tr');
            const price = parseFloat(row.querySelector('.price').textContent.trim());
            const totalAmount = newQuantity * price;
            row.querySelector('.total_amount').textContent = totalAmount.toFixed(2);
            const collected = parseFloat(row.querySelector('.collected').textContent.trim());
            row.querySelector('.remaining').textContent = (totalAmount - collected).toFixed(2);
        } else {
            alert(data.message);
        }
    })
    .catch(error => console.error('Error updating item quantity:', error));
}

function showUserOrders(accountId) {
    fetch('fetch_user_orders.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ account_id: accountId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const tableBody = document.getElementById('userOrdersTableBody');
            tableBody.innerHTML = '';
            let totalQuantity = 0;
            let totalAmount = 0;
            let totalPaid = 0;
            let totalCollected = 0;
            data.orders.forEach(order => {
                const statusClass = order.status === 'confirmed' ? 'confirmed' : order.status === 'pending' ? 'pending' : 'delayed';
                const orderTime = new Date(order.time).toLocaleString('en-US', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', hour12: true });
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${order.sale_id}</td>
                    <td>${order.item_name}</td>
                    <td><span class="status-frame ${statusClass}">${order.status}</span></td>
                    <td>${order.quantity}</td>
                    <td>${order.total_amount}</td>
                    <td>${orderTime}</td>
                `;
                tableBody.appendChild(row);
                totalQuantity += order.quantity;
                totalAmount += parseFloat(order.total_amount);
                totalPaid += parseFloat(order.total_amount);
                totalCollected += parseFloat(order.collected);
            });
            document.getElementById('userTotalQuantity').textContent = `إجمالي الكميات: ${totalQuantity}`;
            document.getElementById('userTotalAmount').textContent = `إجمالي مجموع سعر الأصناف: ${totalAmount}`;
            document.getElementById('userTotalPaid').textContent = `إجمالي المدفوع: ${totalCollected}`;
            document.getElementById('userTotalRemaining').textContent = `الباقي: ${totalPaid - totalCollected}`;
            if ($.fn.DataTable.isDataTable('#userOrdersTable')) {
                $('#userOrdersTable').DataTable().destroy();
            }
            $('#userOrdersTable').DataTable({
                "order": [[5, "asc"]],
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json"
                },
                "autoWidth": false
            });
            document.getElementById('userOrdersModal').style.display = "block";
        } else {
            Swal.fire({
                icon: 'error',
                title: 'فشل في جلب طلبات المستخدم',
                text: 'حدث خطأ أثناء جلب طلبات المستخدم. حاول مرة أخرى.'
            });
        }
    })
    .catch(error => {
        console.error("Error fetching user orders:", error);
    });
}

function closeUserOrdersModal() {
    document.getElementById('userOrdersModal').style.display = 'none';
    if ($.fn.DataTable.isDataTable('#userOrdersTable')) {
        $('#userOrdersTable').DataTable().destroy();
    }
}

document.querySelectorAll('td.username').forEach(td => {
    td.addEventListener('click', function() {
        const encryptedAccountId = this.dataset.accountId;
        showUserOrders(encryptedAccountId);
    });
});

// Optionally, add a one-time click handler to unlock audio (insert somewhere after DOM loads):
document.addEventListener('click', function unlockAudio() {
    const newOrderSound = document.getElementById('newOrderSound');
    newOrderSound.play().then(() => {
        // Audio now unlocked; remove this handler.
        document.removeEventListener('click', unlockAudio);
    }).catch(() => {
        // Ignore errors.
    });
});

function printSelectedReceipts() {
    const selected = document.querySelectorAll('.order-checkbox:checked');
    if(selected.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'اختر طلب واحد على الأقل'
        });
        return;
    }
    const plainAccountId = selected[0].getAttribute('data-plain-account-id');
    const encryptedAccountId = selected[0].getAttribute('data-encrypted-account-id');
    let orderDates = [];
    let valid = true;
    selected.forEach(function(chk) {
        if(chk.getAttribute('data-plain-account-id') !== plainAccountId){
            valid = false;
        }
        orderDates.push(chk.getAttribute('data-order-date'));
    });
    if(!valid){
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'يرجى اختيار طلبات من نفس الحساب'
        });
        return;
    }
    Swal.fire({
        title: 'اختر نوع الفاتورة',
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonText: 'فاتورة A4(PDF)',
        denyButtonText: 'ايصال',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if(result.isConfirmed) {
            window.location.href = 'generate_invoice4.php?account_id=' + encodeURIComponent(encryptedAccountId) +
                                 '&order_dates=' + encodeURIComponent(orderDates.join(','));
        } else if(result.isDenied) {
            window.location.href = 'print_selected_receipts.php?account_id=' + encodeURIComponent(encryptedAccountId) +
                                 '&order_dates=' + encodeURIComponent(orderDates.join(','));
        }
    });
}

// New function to delay selected items via mark_as_delayed_bulk.php (corrected spelling)
function delaySelectedItems() {
    const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
    if (checkboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'اختر صنف واحد على الأقل للتأجيل'
        });
        return;
    }
    let saleIds = [];
    // Changed index from 8 to 9 for status cell
    for (const chk of checkboxes) {
        const row = chk.closest('tr');
        const statusText = row.cells[9].innerText.trim();
        if (statusText !== 'انتظار') {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يمكن تأجيل الأصناف التي حالتها انتظار فقط'
            });
            return;
        }
        saleIds.push(chk.getAttribute('data-sale-id'));
    }
    // ...existing fetch code to send saleIds...
    fetch('mark_as_delayed_bulk.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ sale_ids: saleIds })
    }).then(response => response.json())
      .then(data => {
          if (data.status === 'success') {
              Swal.fire({
                  icon: 'success',
                  title: 'نجاح',
                  text: 'تم تأجيل الأصناف المحددة'
              });
              const orderDate = document.getElementById('order-details-body').dataset.orderDate;
              const encryptedAccountId = document.getElementById('order-details-body').dataset.accountId;
              showOrderDetails(encryptedAccountId, orderDate, false);
          } else {
              Swal.fire({
                  icon: 'error',
                  title: 'خطأ',
                  text: data.message
              });
          }
      })
      .catch(error => console.error('Error:', error));
}

// New function to pay only selected items
function paySelectedItems() {
    const checkboxes = document.querySelectorAll('#order-details-body .order-detail-checkbox:checked');
    if (checkboxes.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'تنبيه',
            text: 'اختر صنف واحد على الأقل للدفع'
        });
        return;
    }
    let saleIds = [];
    // Changed index from 8 to 9 for status cell
    for (const chk of checkboxes) {
        const row = chk.closest('tr');
        const statusText = row.cells[9].innerText.trim();
        if (statusText !== 'انتظار' && statusText !== 'مؤجل') {
            Swal.fire({
                icon: 'warning',
                title: 'تنبيه',
                text: 'يمكن دفع الأصناف التي حالتها انتظار أو مؤجل فقط'
            });
            return;
        }
        saleIds.push(chk.getAttribute('data-sale-id'));
    }
    const orderDetailsBody = document.getElementById('order-details-body');
    const orderDate = orderDetailsBody.dataset.orderDate;
    const encryptedAccountId = orderDetailsBody.dataset.accountId;
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: "لن تتمكن من التراجع عن هذا الإجراء!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، ادفعها!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch('pay_selected_items.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    sale_ids: saleIds,
                    account_id: encryptedAccountId,
                    order_date: orderDate
                })
            }).then(response => response.json())
              .then(data => {
                  if (data.status === 'success') {
                      Swal.fire({
                          icon: 'success',
                          title: 'نجاح',
                          text: 'تم الدفع للأصناف المحددة'
                      });
                      showOrderDetails(encryptedAccountId, orderDate, false);
                      fetchOrders();
                  } else {
                      Swal.fire({
                          icon: 'error',
                          title: 'خطأ',
                          text: data.message
                      });
                  }
              })
              .catch(error => console.error('Error:', error));
        }
    });
}

function handleReturnedKeyPress(event, element, saleId) {
    if (event.key === 'Enter') {
        event.preventDefault();
        updateReturned(element, saleId);
    }
}

function updateReturned(element, saleId) {
    const newVal = parseFloat(element.textContent.trim());
    const oldVal = parseFloat(element.dataset.oldReturned);
    if (isNaN(newVal)) {
        alert('القيمة غير صالحة');
        element.textContent = oldVal;
        return;
    }
    fetch('update_returned.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            sale_id: saleId,
            new_returned: newVal
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update the oldReturned data attribute to newVal on success
            element.dataset.oldReturned = newVal;
            // Optionally, refresh order details
            const orderDate = document.querySelector('#order-details-body').dataset.orderDate;
            const encryptedAccountId = document.querySelector('#order-details-body').dataset.accountId;
            showOrderDetails(encryptedAccountId, orderDate, false);
            fetchOrders();
        } else {
            alert(data.message);
            element.textContent = oldVal;
        }
    })
    .catch(error => {
        console.error('Error updating returned:', error);
        element.textContent = oldVal;
    });
}