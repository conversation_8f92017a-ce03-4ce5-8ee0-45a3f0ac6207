-- إضافة عمود access_type لجدول accounts
-- مع تحديد القيمة الافتراضية كـ ecommerce_system

-- إضافة العمود
ALTER TABLE accounts
ADD COLUMN access_type ENUM('admin_panel', 'delivery_system', 'ecommerce_system') DEFAULT 'ecommerce_system';

-- تحديث الحسابات الموجودة حسب الدور
-- المديرين -> النظام الإداري
UPDATE accounts 
SET access_type = 'admin_panel' 
WHERE role = 'admin';

-- التجار -> المتجر الإلكتروني (افتراضي)
UPDATE accounts 
SET access_type = 'ecommerce_system' 
WHERE role = 'dealer' OR role = 'user' OR role = 'purchaser';

-- إضافة فهرس للعمود الجديد
CREATE INDEX idx_accounts_access_type ON accounts(access_type);

-- عرض النتائج
SELECT 'تم إضافة عمود access_type بنجاح!' as message;

-- عرض توزيع أنواع الوصول
SELECT 
    access_type,
    COUNT(*) as count,
    CASE 
        WHEN access_type = 'admin_panel' THEN 'النظام الإداري'
        WHEN access_type = 'delivery_system' THEN 'نظام التوصيل'
        WHEN access_type = 'ecommerce_system' THEN 'المتجر الإلكتروني'
        ELSE 'غير محدد'
    END as access_type_ar
FROM accounts 
GROUP BY access_type;
