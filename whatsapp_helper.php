<?php
/**
 * WhatsApp Helper Functions
 * مساعد إرسال رسائل واتساب
 */

// إعدادات واتساب
class WhatsAppConfig {
    const TOKEN = 'SH9shkGBdm4x0mygeJcKFeuLrfKXyPxcnGiNiabzsH';
    const BASE_URL = 'https://wapilot.net/api/v1';
    const INSTANCE_ID = 'instance1678';
    const MAX_MESSAGE_LENGTH = 4096;
}

class WhatsAppSender {
    
    /**
     * إرسال رسالة نصية
     */
    public static function sendMessage($instanceId, $chatId, $message) {
        $url = WhatsAppConfig::BASE_URL . "/{$instanceId}/send-message";
        
        $data = array(
            'token' => WhatsAppConfig::TOKEN,
            'chat_id' => $chatId,
            'text' => $message
        );
        
        return self::makeRequest($url, $data);
    }
    
    /**
     * إرسال رسالة ترحيب للعميل الجديد
     */
    public static function sendWelcomeMessage($instanceId, $chatId, $customerName) {
        $message = "🎉 مرحباً {$customerName}!\n\n";
        $message .= "نشكرك لانضمامك إلى نظام إدارة المشتريات\n";
        $message .= "سنقوم بإرسال تحديثات طلباتك عبر واتساب\n\n";
        $message .= "📞 للاستفسارات: اتصل بنا\n";
        $message .= "🕐 ساعات العمل: 9 صباحاً - 6 مساءً";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * إرسال إشعار طلب جديد
     */
    public static function sendNewOrderNotification($instanceId, $chatId, $orderData) {
        $message = "🛒 طلب جديد!\n\n";
        $message .= "📋 رقم الطلب: {$orderData['order_id']}\n";
        $message .= "👤 العميل: {$orderData['customer_name']}\n";
        $message .= "💰 المبلغ: {$orderData['total_amount']} جنيه\n";
        $message .= "📅 التاريخ: " . date('Y-m-d H:i') . "\n\n";
        $message .= "🔗 عرض التفاصيل: {$orderData['order_link']}";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * إرسال تأكيد الطلب للعميل
     */
    public static function sendOrderConfirmation($instanceId, $chatId, $orderData) {
        $message = "✅ تم تأكيد طلبك!\n\n";
        $message .= "📋 رقم الطلب: {$orderData['order_id']}\n";
        $message .= "💰 إجمالي المبلغ: {$orderData['total_amount']} جنيه\n";
        $message .= "🚚 موعد التسليم المتوقع: {$orderData['delivery_date']}\n\n";
        $message .= "📞 للاستفسار: اتصل بنا\n";
        $message .= "شكراً لثقتك بنا! 🙏";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * إرسال تحديث حالة الطلب
     */
    public static function sendOrderStatusUpdate($instanceId, $chatId, $orderData) {
        $statusMessages = [
            'pending' => '⏳ قيد المراجعة',
            'confirmed' => '✅ تم التأكيد',
            'processing' => '🔄 قيد التحضير',
            'shipped' => '🚚 تم الشحن',
            'delivered' => '📦 تم التسليم',
            'cancelled' => '❌ تم الإلغاء'
        ];
        
        $statusText = $statusMessages[$orderData['status']] ?? $orderData['status'];
        
        $message = "📢 تحديث حالة الطلب\n\n";
        $message .= "📋 رقم الطلب: {$orderData['order_id']}\n";
        $message .= "📊 الحالة الجديدة: {$statusText}\n";
        
        if (!empty($orderData['notes'])) {
            $message .= "📝 ملاحظات: {$orderData['notes']}\n";
        }
        
        $message .= "\n📞 للاستفسار: اتصل بنا";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * إرسال تذكير بالدفع
     */
    public static function sendPaymentReminder($instanceId, $chatId, $orderData) {
        $message = "💳 تذكير بالدفع\n\n";
        $message .= "📋 رقم الطلب: {$orderData['order_id']}\n";
        $message .= "💰 المبلغ المستحق: {$orderData['amount_due']} جنيه\n";
        $message .= "📅 تاريخ الاستحقاق: {$orderData['due_date']}\n\n";
        $message .= "🏦 طرق الدفع المتاحة:\n";
        $message .= "• نقداً عند التسليم\n";
        $message .= "• تحويل بنكي\n";
        $message .= "• فودافون كاش\n\n";
        $message .= "📞 للاستفسار: اتصل بنا";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * إرسال الفاتورة
     */
    public static function sendInvoice($instanceId, $chatId, $invoiceData) {
        $message = "🧾 فاتورة الطلب\n\n";
        $message .= "📋 رقم الطلب: {$invoiceData['order_id']}\n";
        $message .= "📅 التاريخ: {$invoiceData['date']}\n";
        $message .= "👤 العميل: {$invoiceData['customer_name']}\n\n";
        
        $message .= "📦 تفاصيل الطلب:\n";
        foreach ($invoiceData['items'] as $item) {
            $message .= "• {$item['name']} × {$item['quantity']} = {$item['total']} جنيه\n";
        }
        
        $message .= "\n💰 الإجمالي: {$invoiceData['total']} جنيه\n";
        $message .= "🔗 رابط الفاتورة: {$invoiceData['invoice_link']}";
        
        return self::sendMessage($instanceId, $chatId, $message);
    }
    
    /**
     * تنسيق رقم الهاتف إلى Chat ID
     */
    public static function formatPhoneNumber($phone) {
        // إذا كان الرقم يحتوي على @c.us فهو جاهز
        if (strpos($phone, '@c.us') !== false) {
            return $phone;
        }
        
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // إضافة كود مصر إذا لم يكن موجود
        if (strlen($phone) == 11 && substr($phone, 0, 1) == '0') {
            $phone = '2' . substr($phone, 1);
        } elseif (strlen($phone) == 10) {
            $phone = '20' . $phone;
        }
        
        // إضافة @c.us في النهاية
        return $phone . '@c.us';
    }
    
    /**
     * التحقق من صحة Chat ID
     */
    public static function isValidChatId($chatId) {
        // التحقق من صيغة @c.us
        if (strpos($chatId, '@c.us') !== false) {
            $phone = str_replace('@c.us', '', $chatId);
            return preg_match('/^20[0-9]{10}$/', $phone);
        }
        
        // التحقق من رقم الهاتف العادي
        $phone = preg_replace('/[^0-9]/', '', $chatId);
        if (strlen($phone) == 11 && substr($phone, 0, 1) == '0') {
            return true;
        } elseif (strlen($phone) == 10 || strlen($phone) == 12) {
            return true;
        }
        
        return false;
    }
    
    /**
     * إجراء طلب HTTP
     */
    private static function makeRequest($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Accept: application/json'
        ));
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return array(
                'success' => false,
                'error' => 'cURL Error: ' . $error,
                'http_code' => $httpCode
            );
        }
        
        $responseData = json_decode($response, true);
        
        return array(
            'success' => ($httpCode == 200),
            'data' => $responseData,
            'http_code' => $httpCode,
            'raw_response' => $response
        );
    }
    
    /**
     * حفظ لوج العمليات
     */
    public static function logOperation($operation, $instanceId, $chatId, $success, $error = null) {
        $logData = array(
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => $operation,
            'instance_id' => $instanceId,
            'chat_id' => $chatId,
            'success' => $success,
            'error' => $error
        );
        
        // يمكنك حفظ اللوج في قاعدة البيانات أو ملف
        $logFile = 'logs/whatsapp_' . date('Y-m-d') . '.log';
        if (!file_exists('logs')) {
            mkdir('logs', 0777, true);
        }
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
    }
}

/**
 * مثال على الاستخدام:
 * 
 * // إرسال رسالة بسيطة
 * $result = WhatsAppSender::sendMessage('instance1678', '<EMAIL>', 'مرحباً!');
 * 
 * // إرسال إشعار طلب جديد
 * $orderData = [
 *     'order_id' => '12345',
 *     'customer_name' => 'أحم�� محمد',
 *     'total_amount' => '500',
 *     'order_link' => 'https://example.com/order/12345'
 * ];
 * $result = WhatsAppSender::sendNewOrderNotification('instance1678', '<EMAIL>', $orderData);
 * 
 * // تنسيق رقم الهاتف تلقائياً
 * $chatId = WhatsAppSender::formatPhoneNumber('01234567890'); // سيصبح <EMAIL>
 * $result = WhatsAppSender::sendMessage('instance1678', $chatId, 'مرحباً!');
 * 
 * // استخدام Instance ID من الإعدادات
 * $result = WhatsAppSender::sendMessage(WhatsAppConfig::INSTANCE_ID, $chatId, 'مرحباً!');
 */
?>