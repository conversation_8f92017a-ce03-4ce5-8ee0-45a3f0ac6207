<?php
/**
 * صفحة نظام التوصيل
 * ملف مؤقت فاضي لنظام التوصيل
 */

include 'db_connection.php';
include 'security.php';

// التحقق من نوع الوصول
if (!isset($_SESSION['access_type']) || $_SESSION['access_type'] !== 'delivery_system') {
    header('Location: index.php');
    exit();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التوصيل</title>
    <link rel="stylesheet" href="web_css/style_web.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .delivery-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 40px;
            text-align: center;
            background: var(--color-secondary);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .delivery-icon {
            font-size: 80px;
            color: #e67e22;
            margin-bottom: 30px;
        }
        .delivery-title {
            font-size: 36px;
            color: var(--text-color);
            margin-bottom: 20px;
            font-weight: bold;
        }
        .delivery-message {
            font-size: 18px;
            color: var(--text-secondary);
            line-height: 1.8;
            margin-bottom: 30px;
        }
        .coming-soon {
            background: linear-gradient(135deg, #e67e22, #f39c12);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 30px;
        }
        .features-list {
            text-align: right;
            max-width: 500px;
            margin: 0 auto;
            background: var(--color-primary);
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid #e67e22;
        }
        .features-list h3 {
            color: #e67e22;
            margin-bottom: 15px;
            text-align: center;
        }
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        .features-list li {
            padding: 8px 0;
            color: var(--text-color);
        }
        .features-list li i {
            color: #e67e22;
            margin-left: 10px;
            width: 20px;
        }
        .back-button {
            background: var(--color-primary);
            color: var(--text-color);
            padding: 12px 25px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-block;
            margin-top: 20px;
        }
        .back-button:hover {
            background: var(--hover-color);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="delivery-container">
        <div class="delivery-icon">
            <i class="fas fa-truck"></i>
        </div>
        
        <h1 class="delivery-title">نظام التوصيل</h1>
        
        <div class="coming-soon">
            <i class="fas fa-clock"></i> قريباً
        </div>
        
        <p class="delivery-message">
            مرحباً بك في نظام التوصيل الخاص بمؤسسة عامر النجار للتكنولوجيا.<br>
            هذا النظام قيد التطوير حالياً وسيكون متاحاً قريباً بميزات متقدمة لإدارة عمليات التوصيل.
        </p>
        
        <div class="features-list">
            <h3><i class="fas fa-star"></i> الميزات القادمة</h3>
            <ul>
                <li><i class="fas fa-route"></i> إدارة مسارات التوصيل</li>
                <li><i class="fas fa-box"></i> تتبع الطلبات</li>
                <li><i class="fas fa-map-marker-alt"></i> تحديد المواقع</li>
                <li><i class="fas fa-clock"></i> جدولة التوصيل</li>
                <li><i class="fas fa-mobile-alt"></i> تطبيق موبايل للموصلين</li>
                <li><i class="fas fa-chart-line"></i> تقارير الأداء</li>
            </ul>
        </div>
        
        <a href="index.php" class="back-button">
            <i class="fas fa-arrow-right"></i> العودة لتسجيل الدخول
        </a>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid var(--border-color);">
            <p style="color: var(--text-secondary); font-size: 14px;">
                <strong>معلومات المستخدم:</strong><br>
                اسم المستخدم: <?php echo htmlspecialchars($_SESSION['username'] ?? 'غير محدد'); ?><br>
                نوع الوصول: نظام التوصيل
            </p>
        </div>
    </div>
</body>
</html>
